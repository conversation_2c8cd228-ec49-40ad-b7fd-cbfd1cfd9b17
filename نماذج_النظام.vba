' نماذج نظام إدارة المدرسة المتوسطة
' هذا الملف يحتوي على كود VBA لإنشاء النماذج والوظائف الأساسية

Option Compare Database
Option Explicit

' ====================
' النموذج الرئيسي
' ====================

Private Sub Form_Load()
    ' تحميل النموذج الرئيسي
    Me.Caption = "نظام إدارة المدرسة المتوسطة - الإصدار 1.0"
    
    ' تحديث معلومات المدرسة
    Call تحديث_معلومات_المدرسة
    
    ' تحديث الإحصائيات
    Call تحديث_الاحصائيات_الرئيسية
End Sub

Private Sub تحديث_معلومات_المدرسة()
    Dim rs As DAO.Recordset
    Set rs = CurrentDb.OpenRecordset("SELECT * FROM المدرسة WHERE حالة_النشاط = True")
    
    If Not rs.EOF Then
        Me.txt_اسم_المدرسة = rs!اسم_المدرسة
        Me.txt_رمز_المدرسة = rs!رمز_المدرسة
        Me.txt_اسم_المدير = rs!اسم_المدير
        Me.txt_اسم_الوكيل = rs!اسم_الوكيل
    End If
    
    rs.Close
    Set rs = Nothing
End Sub

Private Sub تحديث_الاحصائيات_الرئيسية()
    ' عدد الطلاب
    Me.txt_عدد_الطلاب = DCount("رقم_الطالب", "الطلاب", "حالة_النشاط = True")
    
    ' عدد المعلمين
    Me.txt_عدد_المعلمين = DCount("رقم_المعلم", "المعلمين", "حالة_النشاط = True")
    
    ' عدد الصفوف
    Me.txt_عدد_الصفوف = DCount("رقم_الصف", "الصفوف", "حالة_النشاط = True")
    
    ' عدد المواد
    Me.txt_عدد_المواد = DCount("رقم_المادة", "المواد_الدراسية", "حالة_النشاط = True")
End Sub

' ====================
' نموذج إدارة الطلاب
' ====================

Private Sub btn_اضافة_طالب_Click()
    DoCmd.OpenForm "نموذج_اضافة_طالب", acNormal, , , acFormAdd
End Sub

Private Sub btn_تعديل_طالب_Click()
    If IsNull(Me.lst_الطلاب) Then
        MsgBox "يرجى اختيار طالب للتعديل", vbExclamation, "تنبيه"
        Exit Sub
    End If
    
    DoCmd.OpenForm "نموذج_تعديل_طالب", acNormal, , "رقم_الطالب = " & Me.lst_الطلاب
End Sub

Private Sub btn_حذف_طالب_Click()
    If IsNull(Me.lst_الطلاب) Then
        MsgBox "يرجى اختيار طالب للحذف", vbExclamation, "تنبيه"
        Exit Sub
    End If
    
    If MsgBox("هل أنت متأكد من حذف هذا الطالب؟", vbYesNo + vbQuestion, "تأكيد الحذف") = vbYes Then
        CurrentDb.Execute "UPDATE الطلاب SET حالة_النشاط = False WHERE رقم_الطالب = " & Me.lst_الطلاب
        Me.lst_الطلاب.Requery
        MsgBox "تم حذف الطالب بنجاح", vbInformation, "نجح"
    End If
End Sub

' ====================
' نموذج الحضور والغياب
' ====================

Private Sub btn_تسجيل_حضور_Click()
    Dim رقم_الطالب As Long
    Dim التاريخ As Date
    Dim حالة_الحضور As String
    
    ' التحقق من البيانات المطلوبة
    If IsNull(Me.cmb_الصف) Then
        MsgBox "يرجى اختيار الصف", vbExclamation, "تنبيه"
        Exit Sub
    End If
    
    If IsNull(Me.txt_التاريخ) Then
        Me.txt_التاريخ = Date
    End If
    
    التاريخ = Me.txt_التاريخ
    
    ' تسجيل الحضور لجميع طلاب الصف
    Dim rs As DAO.Recordset
    Set rs = CurrentDb.OpenRecordset("SELECT رقم_الطالب FROM الطلاب WHERE رقم_الصف = " & Me.cmb_الصف & " AND حالة_النشاط = True")
    
    Do While Not rs.EOF
        رقم_الطالب = rs!رقم_الطالب
        
        ' التحقق من وجود تسجيل سابق لنفس اليوم
        If DCount("رقم_الحضور", "حضور_الطلاب", "رقم_الطالب = " & رقم_الطالب & " AND التاريخ = #" & التاريخ & "#") = 0 Then
            ' إدراج تسجيل جديد
            CurrentDb.Execute "INSERT INTO حضور_الطلاب (رقم_الطالب, التاريخ, حالة_الحضور, رقم_المعلم_المسجل) VALUES (" & _
                             رقم_الطالب & ", #" & التاريخ & "#, 'حاضر', " & Me.cmb_المعلم & ")"
        End If
        
        rs.MoveNext
    Loop
    
    rs.Close
    Set rs = Nothing
    
    MsgBox "تم تسجيل الحضور بنجاح", vbInformation, "نجح"
    Me.subform_الحضور.Form.Requery
End Sub

Private Sub btn_تسجيل_غياب_Click()
    If IsNull(Me.lst_الطلاب_الحضور) Then
        MsgBox "يرجى اختيار طالب", vbExclamation, "تنبيه"
        Exit Sub
    End If
    
    Dim رقم_الطالب As Long
    Dim التاريخ As Date
    
    رقم_الطالب = Me.lst_الطلاب_الحضور
    التاريخ = Me.txt_التاريخ
    
    ' تحديث حالة الحضور إلى غائب
    CurrentDb.Execute "UPDATE حضور_الطلاب SET حالة_الحضور = 'غائب', سبب_الغياب = '" & Me.txt_سبب_الغياب & "' " & _
                     "WHERE رقم_الطالب = " & رقم_الطالب & " AND التاريخ = #" & التاريخ & "#"
    
    MsgBox "تم تسجيل الغياب بنجاح", vbInformation, "نجح"
    Me.subform_الحضور.Form.Requery
End Sub

' ====================
' نموذج إدخال الدرجات
' ====================

Private Sub btn_حفظ_الدرجات_Click()
    If IsNull(Me.cmb_المادة) Or IsNull(Me.cmb_الصف) Or IsNull(Me.cmb_نوع_التقييم) Then
        MsgBox "يرجى ملء جميع الحقول المطلوبة", vbExclamation, "تنبيه"
        Exit Sub
    End If
    
    Dim rs As DAO.Recordset
    Set rs = Me.subform_الدرجات.Form.RecordsetClone
    
    rs.MoveFirst
    Do While Not rs.EOF
        If Not IsNull(rs!الدرجة) Then
            ' التحقق من صحة الدرجة
            If rs!الدرجة > rs!الدرجة_النهائية Then
                MsgBox "الدرجة المدخلة أكبر من الدرجة النهائية للطالب: " & rs!اسم_الطالب, vbExclamation, "خطأ"
                Exit Sub
            End If
            
            ' حفظ الدرجة
            CurrentDb.Execute "INSERT INTO الدرجات (رقم_الطالب, رقم_المادة, نوع_التقييم, الدرجة, الدرجة_النهائية, التاريخ, العام_الدراسي, الفصل_الدراسي, رقم_المعلم_المدخل) VALUES (" & _
                             rs!رقم_الطالب & ", " & Me.cmb_المادة & ", '" & Me.cmb_نوع_التقييم & "', " & rs!الدرجة & ", " & rs!الدرجة_النهائية & ", #" & Date & "#, '" & _
                             DLookup("قيمة_الاعداد", "اعدادات_النظام", "اسم_الاعداد = 'العام_الدراسي_الحالي'") & "', '" & _
                             DLookup("قيمة_الاعداد", "اعدادات_النظام", "اسم_الاعداد = 'الفصل_الدراسي_الحالي'") & "', " & Me.cmb_المعلم & ")"
        End If
        rs.MoveNext
    Loop
    
    rs.Close
    Set rs = Nothing
    
    MsgBox "تم حفظ الدرجات بنجاح", vbInformation, "نجح"
End Sub

' ====================
' نموذج المخالفات السلوكية
' ====================

Private Sub btn_اضافة_مخالفة_Click()
    If IsNull(Me.cmb_الطالب) Or IsNull(Me.cmb_نوع_المخالفة) Or IsNull(Me.txt_وصف_المخالفة) Then
        MsgBox "يرجى ملء جميع الحقول المطلوبة", vbExclamation, "تنبيه"
        Exit Sub
    End If
    
    CurrentDb.Execute "INSERT INTO المخالفات_السلوكية (رقم_الطالب, نوع_المخالفة, وصف_المخالفة, درجة_المخالفة, التاريخ, رقم_المعلم_المبلغ) VALUES (" & _
                     Me.cmb_الطالب & ", '" & Me.cmb_نوع_المخالفة & "', '" & Me.txt_وصف_المخالفة & "', '" & Me.cmb_درجة_المخالفة & "', #" & Date & "#, " & Me.cmb_المعلم_المبلغ & ")"
    
    MsgBox "تم إضافة المخالفة بنجاح", vbInformation, "نجح"
    
    ' إرسال إشعار لولي الأمر
    Call ارسال_اشعار_مخالفة(Me.cmb_الطالب, Me.cmb_نوع_المخالفة, Me.txt_وصف_المخالفة)
    
    ' مسح النموذج
    Me.cmb_الطالب = Null
    Me.cmb_نوع_المخالفة = Null
    Me.txt_وصف_المخالفة = Null
    Me.cmb_درجة_المخالفة = "بسيطة"
    
    Me.subform_المخالفات.Form.Requery
End Sub

Private Sub ارسال_اشعار_مخالفة(رقم_الطالب As Long, نوع_المخالفة As String, وصف_المخالفة As String)
    Dim اسم_الطالب As String
    Dim رقم_جوال_ولي_الامر As String
    Dim نص_الرسالة As String
    
    ' الحصول على بيانات الطالب
    اسم_الطالب = DLookup("الاسم_الكامل", "الطلاب", "رقم_الطالب = " & رقم_الطالب)
    رقم_جوال_ولي_الامر = DLookup("رقم_جوال_ولي_الامر", "الطلاب", "رقم_الطالب = " & رقم_الطالب)
    
    ' تكوين نص الرسالة
    نص_الرسالة = "عزيزي ولي الأمر، نود إعلامكم بأن الطالب/ة " & اسم_الطالب & " قد ارتكب مخالفة: " & نوع_المخالفة & ". " & وصف_المخالفة & ". إدارة المدرسة"
    
    ' حفظ الإشعار في قاعدة البيانات
    CurrentDb.Execute "INSERT INTO الاشعارات (نوع_الاشعار, العنوان, المحتوى, المرسل_اليه, تاريخ_الارسال) VALUES ('رسالة', 'إشعار مخالفة سلوكية', '" & نص_الرسالة & "', 'أولياء أمور', #" & Now & "#)"
End Sub

' ====================
' وظائف استيراد البيانات من نور
' ====================

Public Sub استيراد_من_اكسل()
    Dim fd As FileDialog
    Dim ملف_الاكسل As String
    
    Set fd = Application.FileDialog(msoFileDialogFilePicker)
    fd.Title = "اختر ملف Excel لاستيراد بيانات الطلاب"
    fd.Filters.Add "ملفات Excel", "*.xlsx;*.xls"
    
    If fd.Show = -1 Then
        ملف_الاكسل = fd.SelectedItems(1)
        Call استيراد_بيانات_الطلاب_من_اكسل(ملف_الاكسل)
    End If
    
    Set fd = Nothing
End Sub

Private Sub استيراد_بيانات_الطلاب_من_اكسل(مسار_الملف As String)
    Dim xl As Object
    Dim wb As Object
    Dim ws As Object
    Dim i As Long
    Dim آخر_صف As Long
    
    ' فتح Excel
    Set xl = CreateObject("Excel.Application")
    Set wb = xl.Workbooks.Open(مسار_الملف)
    Set ws = wb.Worksheets(1)
    
    ' العثور على آخر صف يحتوي على بيانات
    آخر_صف = ws.Cells(ws.Rows.Count, 1).End(-4162).Row ' xlUp = -4162
    
    ' بدء الاستيراد من الصف الثاني (تجاهل العناوين)
    For i = 2 To آخر_صف
        ' التحقق من وجود رقم الهوية
        If Not IsEmpty(ws.Cells(i, 2).Value) Then
            ' التحقق من عدم وجود الطالب مسبقاً
            If DCount("رقم_الطالب", "الطلاب", "رقم_الهوية = '" & ws.Cells(i, 2).Value & "'") = 0 Then
                ' إدراج الطالب الجديد
                CurrentDb.Execute "INSERT INTO الطلاب (رقم_الهوية, الاسم_الكامل, تاريخ_الميلاد, رقم_الصف, اسم_ولي_الامر, رقم_جوال_ولي_الامر, العام_الدراسي, الفصل_الدراسي) VALUES ('" & _
                                 ws.Cells(i, 2).Value & "', '" & ws.Cells(i, 3).Value & "', #" & ws.Cells(i, 4).Value & "#, " & ws.Cells(i, 5).Value & ", '" & _
                                 ws.Cells(i, 6).Value & "', '" & ws.Cells(i, 7).Value & "', '" & _
                                 DLookup("قيمة_الاعداد", "اعدادات_النظام", "اسم_الاعداد = 'العام_الدراسي_الحالي'") & "', '" & _
                                 DLookup("قيمة_الاعداد", "اعدادات_النظام", "اسم_الاعداد = 'الفصل_الدراسي_الحالي'") & "')"
            End If
        End If
    Next i
    
    ' إغلاق Excel
    wb.Close False
    xl.Quit
    Set ws = Nothing
    Set wb = Nothing
    Set xl = Nothing
    
    MsgBox "تم استيراد البيانات بنجاح", vbInformation, "نجح"
End Sub

' ====================
' وظائف التقارير
' ====================

Public Sub طباعة_كشف_الحضور()
    If IsNull(Forms!نموذج_الحضور!cmb_الصف) Then
        MsgBox "يرجى اختيار الصف", vbExclamation, "تنبيه"
        Exit Sub
    End If
    
    DoCmd.OpenReport "تقرير_كشف_الحضور", acViewPreview, , "رقم_الصف = " & Forms!نموذج_الحضور!cmb_الصف
End Sub

Public Sub طباعة_كشف_الدرجات()
    If IsNull(Forms!نموذج_الدرجات!cmb_الصف) Or IsNull(Forms!نموذج_الدرجات!cmb_المادة) Then
        MsgBox "يرجى اختيار الصف والمادة", vbExclamation, "تنبيه"
        Exit Sub
    End If
    
    DoCmd.OpenReport "تقرير_كشف_الدرجات", acViewPreview, , "رقم_الصف = " & Forms!نموذج_الدرجات!cmb_الصف & " AND رقم_المادة = " & Forms!نموذج_الدرجات!cmb_المادة
End Sub

Public Sub طباعة_شهادة_طالب()
    If IsNull(Forms!نموذج_الطلاب!lst_الطلاب) Then
        MsgBox "يرجى اختيار طالب", vbExclamation, "تنبيه"
        Exit Sub
    End If
    
    DoCmd.OpenReport "تقرير_شهادة_الطالب", acViewPreview, , "رقم_الطالب = " & Forms!نموذج_الطلاب!lst_الطلاب
End Sub

' ====================
' وظائف إعداد اللجان
' ====================

Public Sub انشاء_لجان_الاختبارات()
    Dim نوع_الاختبار As String
    Dim تاريخ_الاختبار As Date
    Dim عدد_الطلاب_في_اللجنة As Integer
    
    نوع_الاختبار = InputBox("أدخل نوع الاختبار (شهري/نهائي/دور ثاني):", "نوع الاختبار", "نهائي")
    تاريخ_الاختبار = CDate(InputBox("أدخل تاريخ الاختبار (dd/mm/yyyy):", "تاريخ الاختبار", Date))
    عدد_الطلاب_في_اللجنة = CInt(InputBox("أدخل عدد الطلاب في كل لجنة:", "عدد الطلاب", "30"))
    
    ' حساب عدد اللجان المطلوبة
    Dim عدد_الطلاب_الكلي As Integer
    Dim عدد_اللجان As Integer
    
    عدد_الطلاب_الكلي = DCount("رقم_الطالب", "الطلاب", "حالة_النشاط = True")
    عدد_اللجان = Int(عدد_الطلاب_الكلي / عدد_الطلاب_في_اللجنة) + 1
    
    ' إنشاء اللجان
    Dim i As Integer
    For i = 1 To عدد_اللجان
        CurrentDb.Execute "INSERT INTO لجان_الاختبارات (اسم_اللجنة, رقم_القاعة, العدد_الاقصى, نوع_الاختبار, تاريخ_الاختبار, العام_الدراسي, الفصل_الدراسي) VALUES ('لجنة " & i & "', 'قاعة " & i & "', " & عدد_الطلاب_في_اللجنة & ", '" & نوع_الاختبار & "', #" & تاريخ_الاختبار & "#, '" & _
                         DLookup("قيمة_الاعداد", "اعدادات_النظام", "اسم_الاعداد = 'العام_الدراسي_الحالي'") & "', '" & _
                         DLookup("قيمة_الاعداد", "اعدادات_النظام", "اسم_الاعداد = 'الفصل_الدراسي_الحالي'") & "')"
    Next i
    
    MsgBox "تم إنشاء " & عدد_اللجان & " لجنة بنجاح", vbInformation, "نجح"
    
    ' توزيع الطلاب على اللجان
    Call توزيع_الطلاب_على_اللجان(نوع_الاختبار, تاريخ_الاختبار)
End Sub

Private Sub توزيع_الطلاب_على_اللجان(نوع_الاختبار As String, تاريخ_الاختبار As Date)
    Dim rs_طلاب As DAO.Recordset
    Dim rs_لجان As DAO.Recordset
    Dim رقم_اللجنة_الحالية As Long
    Dim رقم_الجلوس As Integer
    Dim عداد_الطلاب_في_اللجنة As Integer
    
    ' فتح recordset للطلاب
    Set rs_طلاب = CurrentDb.OpenRecordset("SELECT رقم_الطالب FROM الطلاب WHERE حالة_النشاط = True ORDER BY الاسم_الكامل")
    
    ' فتح recordset للجان
    Set rs_لجان = CurrentDb.OpenRecordset("SELECT رقم_اللجنة, العدد_الاقصى FROM لجان_الاختبارات WHERE نوع_الاختبار = '" & نوع_الاختبار & "' AND تاريخ_الاختبار = #" & تاريخ_الاختبار & "# ORDER BY رقم_اللجنة")
    
    If Not rs_لجان.EOF Then
        رقم_اللجنة_الحالية = rs_لجان!رقم_اللجنة
        رقم_الجلوس = 1
        عداد_الطلاب_في_اللجنة = 0
        
        Do While Not rs_طلاب.EOF
            ' التحقق من امتلاء اللجنة الحالية
            If عداد_الطلاب_في_اللجنة >= rs_لجان!العدد_الاقصى Then
                rs_لجان.MoveNext
                If Not rs_لجان.EOF Then
                    رقم_اللجنة_الحالية = rs_لجان!رقم_اللجنة
                    رقم_الجلوس = 1
                    عداد_الطلاب_في_اللجنة = 0
                Else
                    Exit Do ' لا توجد لجان أخرى
                End If
            End If
            
            ' إضافة الطالب إلى اللجنة
            CurrentDb.Execute "INSERT INTO توزيع_اللجان (رقم_الطالب, رقم_اللجنة, رقم_الجلوس, رقم_المادة) VALUES (" & _
                             rs_طلاب!رقم_الطالب & ", " & رقم_اللجنة_الحالية & ", " & رقم_الجلوس & ", 1)"
            
            رقم_الجلوس = رقم_الجلوس + 1
            عداد_الطلاب_في_اللجنة = عداد_الطلاب_في_اللجنة + 1
            
            rs_طلاب.MoveNext
        Loop
    End If
    
    rs_طلاب.Close
    rs_لجان.Close
    Set rs_طلاب = Nothing
    Set rs_لجان = Nothing
    
    MsgBox "تم توزيع الطلاب على اللجان بنجاح", vbInformation, "نجح"
End Sub

' ====================
' وظائف إضافية لاستيراد البيانات من نور
' ====================

Public Sub استيراد_من_CSV()
    Dim fd As FileDialog
    Dim ملف_CSV As String

    Set fd = Application.FileDialog(msoFileDialogFilePicker)
    fd.Title = "اختر ملف CSV لاستيراد بيانات الطلاب من نور"
    fd.Filters.Add "ملفات CSV", "*.csv"

    If fd.Show = -1 Then
        ملف_CSV = fd.SelectedItems(1)
        Call استيراد_بيانات_من_CSV(ملف_CSV)
    End If

    Set fd = Nothing
End Sub

Private Sub استيراد_بيانات_من_CSV(مسار_الملف As String)
    Dim ملف_رقم As Integer
    Dim سطر As String
    Dim حقول() As String
    Dim عداد_السطور As Long
    Dim عداد_المستوردة As Long

    ملف_رقم = FreeFile
    Open مسار_الملف For Input As #ملف_رقم

    ' تجاهل السطر الأول (العناوين)
    If Not EOF(ملف_رقم) Then
        Line Input #ملف_رقم, سطر
    End If

    عداد_السطور = 0
    عداد_المستوردة = 0

    Do While Not EOF(ملف_رقم)
        Line Input #ملف_رقم, سطر
        عداد_السطور = عداد_السطور + 1

        ' تقسيم السطر إلى حقول
        حقول = Split(سطر, ",")

        If UBound(حقول) >= 6 Then ' التأكد من وجود الحقول المطلوبة
            ' التحقق من عدم وجود الطالب مسبقاً
            If DCount("رقم_الطالب", "الطلاب", "رقم_الهوية = '" & Trim(حقول(1)) & "'") = 0 Then
                ' إدراج الطالب الجديد
                On Error Resume Next
                CurrentDb.Execute "INSERT INTO الطلاب (رقم_الهوية, الاسم_الكامل, تاريخ_الميلاد, رقم_الصف, اسم_ولي_الامر, رقم_جوال_ولي_الامر, العام_الدراسي, الفصل_الدراسي) VALUES ('" & _
                                 Trim(حقول(1)) & "', '" & Trim(حقول(2)) & "', #" & CDate(Trim(حقول(3))) & "#, " & CLng(Trim(حقول(4))) & ", '" & _
                                 Trim(حقول(5)) & "', '" & Trim(حقول(6)) & "', '" & _
                                 DLookup("قيمة_الاعداد", "اعدادات_النظام", "اسم_الاعداد = 'العام_الدراسي_الحالي'") & "', '" & _
                                 DLookup("قيمة_الاعداد", "اعدادات_النظام", "اسم_الاعداد = 'الفصل_الدراسي_الحالي'") & "')"

                If Err.Number = 0 Then
                    عداد_المستوردة = عداد_المستوردة + 1
                End If
                On Error GoTo 0
            End If
        End If
    Loop

    Close #ملف_رقم

    MsgBox "تم استيراد " & عداد_المستوردة & " طالب من أصل " & عداد_السطور & " سطر", vbInformation, "نجح الاستيراد"
End Sub

Public Sub تصدير_الى_نور()
    Dim مسار_التصدير As String
    Dim ملف_رقم As Integer
    Dim rs As DAO.Recordset

    ' اختيار مكان حفظ الملف
    مسار_التصدير = Application.GetSaveAsFilename(InitialFileName:="تصدير_الطلاب_" & Format(Date, "yyyy-mm-dd") & ".csv", _
                                                   FileFilter:="ملفات CSV (*.csv), *.csv")

    If مسار_التصدير = "False" Then Exit Sub

    ملف_رقم = FreeFile
    Open مسار_التصدير For Output As #ملف_رقم

    ' كتابة العناوين
    Print #ملف_رقم, "رقم_الطالب,رقم_الهوية,الاسم_الكامل,تاريخ_الميلاد,اسم_الصف,اسم_ولي_الامر,رقم_جوال_ولي_الامر,العنوان"

    ' استعلام البيانات
    Set rs = CurrentDb.OpenRecordset("SELECT p.رقم_الطالب, p.رقم_الهوية, p.الاسم_الكامل, p.تاريخ_الميلاد, c.اسم_الصف, p.اسم_ولي_الامر, p.رقم_جوال_ولي_الامر, p.العنوان " & _
                                    "FROM الطلاب p INNER JOIN الصفوف c ON p.رقم_الصف = c.رقم_الصف " & _
                                    "WHERE p.حالة_النشاط = True ORDER BY c.اسم_الصف, p.الاسم_الكامل")

    Do While Not rs.EOF
        Print #ملف_رقم, rs!رقم_الطالب & "," & rs!رقم_الهوية & "," & rs!الاسم_الكامل & "," & _
                          Format(rs!تاريخ_الميلاد, "yyyy-mm-dd") & "," & rs!اسم_الصف & "," & _
                          rs!اسم_ولي_الامر & "," & rs!رقم_جوال_ولي_الامر & "," & Nz(rs!العنوان, "")
        rs.MoveNext
    Loop

    rs.Close
    Set rs = Nothing
    Close #ملف_رقم

    MsgBox "تم تصدير البيانات بنجاح إلى: " & مسار_التصدير, vbInformation, "نجح التصدير"
End Sub

' ====================
' وظائف إرسال الرسائل والإشعارات
' ====================

Public Sub ارسال_رسالة_جماعية()
    Dim نوع_المستقبل As String
    Dim عنوان_الرسالة As String
    Dim محتوى_الرسالة As String
    Dim عدد_المرسل_اليهم As Long

    نوع_المستقبل = InputBox("أدخل نوع المستقبل (طلاب/معلمين/أولياء أمور/الكل):", "نوع المستقبل", "أولياء أمور")
    عنوان_الرسالة = InputBox("أدخل عنوان الرسالة:", "عنوان الرسالة")
    محتوى_الرسالة = InputBox("أدخل محتوى الرسالة:", "محتوى الرسالة")

    If عنوان_الرسالة = "" Or محتوى_الرسالة = "" Then
        MsgBox "يرجى ملء جميع الحقول", vbExclamation, "تنبيه"
        Exit Sub
    End If

    ' حساب عدد المستقبلين
    Select Case نوع_المستقبل
        Case "طلاب"
            عدد_المرسل_اليهم = DCount("رقم_الطالب", "الطلاب", "حالة_النشاط = True")
        Case "معلمين"
            عدد_المرسل_اليهم = DCount("رقم_المعلم", "المعلمين", "حالة_النشاط = True")
        Case "أولياء أمور"
            عدد_المرسل_اليهم = DCount("رقم_الطالب", "الطلاب", "حالة_النشاط = True AND رقم_جوال_ولي_الامر IS NOT NULL")
        Case "الكل"
            عدد_المرسل_اليهم = DCount("رقم_الطالب", "الطلاب", "حالة_النشاط = True") + DCount("رقم_المعلم", "المعلمين", "حالة_النشاط = True")
    End Select

    ' حفظ الإشعار
    CurrentDb.Execute "INSERT INTO الاشعارات (نوع_الاشعار, العنوان, المحتوى, المرسل_اليه, عدد_المستقبلين, رقم_المرسل) VALUES ('رسالة', '" & _
                     عنوان_الرسالة & "', '" & محتوى_الرسالة & "', '" & نوع_المستقبل & "', " & عدد_المرسل_اليهم & ", 1)"

    MsgBox "تم إرسال الرسالة إلى " & عدد_المرسل_اليهم & " مستقبل", vbInformation, "نجح الإرسال"
End Sub

Public Sub ارسال_اشعار_درجات()
    Dim رقم_الصف As Long
    Dim رقم_المادة As Long
    Dim rs As DAO.Recordset
    Dim اسم_الطالب As String
    Dim اسم_المادة As String
    Dim الدرجة As Double
    Dim رقم_جوال_ولي_الامر As String
    Dim نص_الرسالة As String

    رقم_الصف = CLng(InputBox("أدخل رقم الصف:", "رقم الصف"))
    رقم_المادة = CLng(InputBox("أدخل رقم المادة:", "رقم المادة"))

    ' استعلام الدرجات
    Set rs = CurrentDb.OpenRecordset("SELECT p.الاسم_الكامل, p.رقم_جوال_ولي_الامر, m.اسم_المادة, g.الدرجة, g.الدرجة_النهائية " & _
                                    "FROM ((الطلاب p INNER JOIN الدرجات g ON p.رقم_الطالب = g.رقم_الطالب) " & _
                                    "INNER JOIN المواد_الدراسية m ON g.رقم_المادة = m.رقم_المادة) " & _
                                    "WHERE p.رقم_الصف = " & رقم_الصف & " AND g.رقم_المادة = " & رقم_المادة & " " & _
                                    "AND g.نوع_التقييم = 'اختبار نهائي' AND p.حالة_النشاط = True")

    Do While Not rs.EOF
        اسم_الطالب = rs!الاسم_الكامل
        اسم_المادة = rs!اسم_المادة
        الدرجة = rs!الدرجة
        رقم_جوال_ولي_الامر = rs!رقم_جوال_ولي_الامر

        ' تكوين نص الرسالة
        نص_الرسالة = "عزيزي ولي الأمر، نود إعلامكم بأن الطالب/ة " & اسم_الطالب & " حصل على درجة " & الدرجة & " من " & rs!الدرجة_النهائية & " في مادة " & اسم_المادة & ". إدارة المدرسة"

        ' حفظ الإشعار
        CurrentDb.Execute "INSERT INTO الاشعارات (نوع_الاشعار, العنوان, المحتوى, المرسل_اليه) VALUES ('إشعار', 'نتيجة الاختبار', '" & نص_الرسالة & "', 'أولياء أمور')"

        rs.MoveNext
    Loop

    rs.Close
    Set rs = Nothing

    MsgBox "تم إرسال إشعارات الدرجات بنجاح", vbInformation, "نجح الإرسال"
End Sub

' ====================
' وظائف النسخ الاحتياطي والاستعادة
' ====================

Public Sub انشاء_نسخة_احتياطية()
    Dim مسار_النسخة As String
    Dim اسم_الملف As String

    اسم_الملف = "نسخة_احتياطية_" & Format(Now, "yyyy-mm-dd_hh-nn-ss") & ".accdb"
    مسار_النسخة = Application.GetSaveAsFilename(InitialFileName:=اسم_الملف, _
                                                FileFilter:="ملفات Access (*.accdb), *.accdb")

    If مسار_النسخة = "False" Then Exit Sub

    ' نسخ قاعدة البيانات الحالية
    FileCopy CurrentDb.Name, مسار_النسخة

    MsgBox "تم إنشاء النسخة الاحتياطية بنجاح في: " & مسار_النسخة, vbInformation, "نجح النسخ الاحتياطي"
End Sub

Public Sub استعادة_نسخة_احتياطية()
    Dim مسار_النسخة As String

    مسار_النسخة = Application.GetOpenFilename(FileFilter:="ملفات Access (*.accdb), *.accdb", Title:="اختر النسخة الاحتياطية للاستعادة")

    If مسار_النسخة = "False" Then Exit Sub

    If MsgBox("تحذير: سيتم استبدال قاعدة البيانات الحالية بالنسخة الاحتياطية. هل أنت متأكد؟", vbYesNo + vbExclamation, "تأكيد الاستعادة") = vbYes Then
        ' إغلاق قاعدة البيانات الحالية
        Application.Quit acQuitSaveAll

        ' نسخ النسخة الاحتياطية مكان الملف الحالي
        ' ملاحظة: هذا يتطلب إعادة تشغيل التطبيق
        FileCopy مسار_النسخة, CurrentDb.Name
    End If
End Sub

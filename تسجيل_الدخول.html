<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة المدرسة المتوسطة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 450px;
            backdrop-filter: blur(10px);
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .login-header .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 10px 30px rgba(76, 175, 80, 0.3);
        }
        
        .login-header .logo i {
            font-size: 2.5rem;
            color: white;
        }
        
        .login-header h1 {
            color: #333;
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .login-header p {
            color: #666;
            font-size: 1rem;
            margin: 0;
        }
        
        .form-group {
            margin-bottom: 25px;
            position: relative;
        }
        
        .form-control {
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            padding: 15px 20px 15px 50px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        
        .form-control:focus {
            border-color: #4CAF50;
            box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25);
            background: white;
        }
        
        .form-icon {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            font-size: 1.1rem;
            z-index: 10;
        }
        
        .btn-login {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            border: none;
            border-radius: 12px;
            padding: 15px;
            font-size: 1.1rem;
            font-weight: bold;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
        }
        
        .btn-login:hover {
            background: linear-gradient(45deg, #45a049, #4CAF50);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
            color: white;
        }
        
        .btn-login:active {
            transform: translateY(0);
        }
        
        .user-type-selector {
            margin-bottom: 25px;
        }
        
        .user-type-option {
            background: #f8f9fa;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .user-type-option:hover {
            border-color: #4CAF50;
            background: rgba(76, 175, 80, 0.1);
        }
        
        .user-type-option.active {
            border-color: #4CAF50;
            background: rgba(76, 175, 80, 0.1);
            color: #4CAF50;
            font-weight: bold;
        }
        
        .user-type-option input[type="radio"] {
            display: none;
        }
        
        .user-type-option i {
            font-size: 1.5rem;
            margin-left: 10px;
        }
        
        .forgot-password {
            text-align: center;
            margin-top: 20px;
        }
        
        .forgot-password a {
            color: #4CAF50;
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }
        
        .forgot-password a:hover {
            color: #45a049;
            text-decoration: underline;
        }
        
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
            color: #666;
            font-size: 0.9rem;
        }
        
        .alert {
            border-radius: 12px;
            margin-bottom: 20px;
        }
        
        .loading {
            display: none;
        }
        
        .loading .spinner-border {
            width: 1.5rem;
            height: 1.5rem;
            margin-left: 10px;
        }
        
        @media (max-width: 576px) {
            .login-container {
                margin: 20px;
                padding: 30px 25px;
            }
            
            .login-header h1 {
                font-size: 1.5rem;
            }
        }
        
        /* تأثيرات الحركة */
        .login-container {
            animation: slideUp 0.6s ease-out;
        }
        
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .form-group {
            animation: fadeIn 0.8s ease-out;
            animation-fill-mode: both;
        }
        
        .form-group:nth-child(1) { animation-delay: 0.1s; }
        .form-group:nth-child(2) { animation-delay: 0.2s; }
        .form-group:nth-child(3) { animation-delay: 0.3s; }
        .form-group:nth-child(4) { animation-delay: 0.4s; }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="logo">
                <i class="fas fa-school"></i>
            </div>
            <h1>نظام إدارة المدرسة المتوسطة</h1>
            <p>مرحباً بك، يرجى تسجيل الدخول للمتابعة</p>
        </div>
        
        <div id="alertContainer"></div>
        
        <form id="loginForm">
            <div class="form-group">
                <label class="form-label">نوع المستخدم</label>
                <div class="user-type-selector">
                    <label class="user-type-option active">
                        <input type="radio" name="userType" value="admin" checked>
                        <i class="fas fa-user-tie"></i>
                        مدير المدرسة
                    </label>
                    <label class="user-type-option">
                        <input type="radio" name="userType" value="deputy">
                        <i class="fas fa-user-cog"></i>
                        وكيل المدرسة
                    </label>
                    <label class="user-type-option">
                        <input type="radio" name="userType" value="teacher">
                        <i class="fas fa-chalkboard-teacher"></i>
                        معلم
                    </label>
                    <label class="user-type-option">
                        <input type="radio" name="userType" value="staff">
                        <i class="fas fa-user"></i>
                        موظف
                    </label>
                </div>
            </div>
            
            <div class="form-group">
                <i class="fas fa-user form-icon"></i>
                <input type="text" class="form-control" id="username" name="username" placeholder="اسم المستخدم" required>
            </div>
            
            <div class="form-group">
                <i class="fas fa-lock form-icon"></i>
                <input type="password" class="form-control" id="password" name="password" placeholder="كلمة المرور" required>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn btn-login">
                    <span class="login-text">تسجيل الدخول</span>
                    <span class="loading">
                        <span class="spinner-border spinner-border-sm" role="status"></span>
                        جاري التحقق...
                    </span>
                </button>
            </div>
        </form>
        
        <div class="forgot-password">
            <a href="#" onclick="forgotPassword()">نسيت كلمة المرور؟</a>
        </div>
        
        <div class="footer">
            <p>&copy; 2024 نظام إدارة المدرسة المتوسطة</p>
            <p>الإصدار 1.0</p>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تفعيل اختيار نوع المستخدم
        document.querySelectorAll('.user-type-option').forEach(option => {
            option.addEventListener('click', function() {
                // إزالة التفعيل من جميع الخيارات
                document.querySelectorAll('.user-type-option').forEach(opt => {
                    opt.classList.remove('active');
                });
                
                // تفعيل الخيار المحدد
                this.classList.add('active');
                this.querySelector('input[type="radio"]').checked = true;
            });
        });
        
        // معالجة تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const userType = document.querySelector('input[name="userType"]:checked').value;
            
            // التحقق من البيانات
            if (!username || !password) {
                showAlert('يرجى ملء جميع الحقول المطلوبة', 'danger');
                return;
            }
            
            // إظهار حالة التحميل
            showLoading(true);
            
            // محاكاة عملية التحقق
            setTimeout(() => {
                if (validateLogin(username, password, userType)) {
                    showAlert('تم تسجيل الدخول بنجاح', 'success');
                    setTimeout(() => {
                        window.location.href = 'الواجهة_الرئيسية.html';
                    }, 1500);
                } else {
                    showAlert('اسم المستخدم أو كلمة المرور غير صحيحة', 'danger');
                    showLoading(false);
                }
            }, 2000);
        });
        
        function validateLogin(username, password, userType) {
            // بيانات تجريبية للاختبار
            const users = {
                'admin': { password: 'admin123', type: 'admin' },
                'deputy': { password: 'deputy123', type: 'deputy' },
                'teacher': { password: 'teacher123', type: 'teacher' },
                'staff': { password: 'staff123', type: 'staff' }
            };
            
            return users[username] && users[username].password === password && users[username].type === userType;
        }
        
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            alertContainer.innerHTML = alertHtml;
            
            // إزالة التنبيه تلقائياً بعد 5 ثوان
            setTimeout(() => {
                const alert = alertContainer.querySelector('.alert');
                if (alert) {
                    alert.remove();
                }
            }, 5000);
        }
        
        function showLoading(show) {
            const loginText = document.querySelector('.login-text');
            const loading = document.querySelector('.loading');
            const submitBtn = document.querySelector('.btn-login');
            
            if (show) {
                loginText.style.display = 'none';
                loading.style.display = 'inline-block';
                submitBtn.disabled = true;
            } else {
                loginText.style.display = 'inline-block';
                loading.style.display = 'none';
                submitBtn.disabled = false;
            }
        }
        
        function forgotPassword() {
            const email = prompt('يرجى إدخال البريد الإلكتروني المرتبط بحسابك:');
            if (email) {
                showAlert('تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني', 'info');
            }
        }
        
        // تأثيرات بصرية إضافية
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.querySelector('.form-icon').style.color = '#4CAF50';
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.querySelector('.form-icon').style.color = '#666';
            });
        });
        
        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', function(e) {
            // Enter للتسجيل
            if (e.key === 'Enter' && e.target.tagName !== 'BUTTON') {
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            }
            
            // Escape لمسح النموذج
            if (e.key === 'Escape') {
                document.getElementById('username').value = '';
                document.getElementById('password').value = '';
                document.getElementById('username').focus();
            }
        });
        
        // تركيز تلقائي على حقل اسم المستخدم
        window.addEventListener('load', function() {
            document.getElementById('username').focus();
        });
    </script>
</body>
</html>

-- قاعدة بيانات نظام إدارة المدرسة المتوسطة
-- هذا ملف SQL لإنشاء قاعدة البيانات في Access

-- جدول المدرسة
CREATE TABLE المدرسة (
    رقم_المدرسة AUTOINCREMENT PRIMARY KEY,
    اسم_المدرسة TEXT(100) NOT NULL,
    رمز_المدرسة TEXT(20) UNIQUE NOT NULL,
    العنوان TEXT(200),
    رقم_الهاتف TEXT(20),
    رقم_الفاكس TEXT(20),
    البريد_الالكتروني TEXT(100),
    اسم_المدير TEXT(50),
    اسم_الوكيل TEXT(50),
    المرحلة_الدراسية TEXT(20) DEFAULT 'متوسطة',
    تاريخ_الانشاء DATETIME DEFAULT NOW(),
    حالة_النشاط YESNO DEFAULT TRUE
);

-- جدو<PERSON> المعلمين
CREATE TABLE المعلمين (
    رقم_المعلم AUTOINCREMENT PRIMARY KEY,
    الرقم_الوظيفي TEXT(20) UNIQUE NOT NULL,
    الاسم_الكامل TEXT(100) NOT NULL,
    رقم_الهوية TEXT(20) UNIQUE NOT NULL,
    تاريخ_الميلاد DATETIME,
    الجنسية TEXT(30) DEFAULT 'سعودي',
    رقم_الجوال TEXT(15),
    البريد_الالكتروني TEXT(100),
    العنوان TEXT(200),
    المؤهل_العلمي TEXT(50),
    التخصص TEXT(50),
    تاريخ_التعيين DATETIME,
    الراتب_الاساسي CURRENCY,
    حالة_الخدمة TEXT(20) DEFAULT 'على رأس العمل',
    ملاحظات MEMO,
    تاريخ_الاضافة DATETIME DEFAULT NOW(),
    حالة_النشاط YESNO DEFAULT TRUE
);

-- جدول الصفوف الدراسية
CREATE TABLE الصفوف (
    رقم_الصف AUTOINCREMENT PRIMARY KEY,
    اسم_الصف TEXT(50) NOT NULL,
    المستوى TEXT(20) NOT NULL, -- أول متوسط، ثاني متوسط، ثالث متوسط
    الشعبة TEXT(10) NOT NULL, -- أ، ب، ج، د
    رقم_المعلم_المشرف LONG,
    العدد_الاقصى_للطلاب INTEGER DEFAULT 30,
    العدد_الحالي_للطلاب INTEGER DEFAULT 0,
    العام_الدراسي TEXT(20),
    الفصل_الدراسي TEXT(20),
    ملاحظات MEMO,
    تاريخ_الاضافة DATETIME DEFAULT NOW(),
    حالة_النشاط YESNO DEFAULT TRUE,
    FOREIGN KEY (رقم_المعلم_المشرف) REFERENCES المعلمين(رقم_المعلم)
);

-- جدول المواد الدراسية
CREATE TABLE المواد_الدراسية (
    رقم_المادة AUTOINCREMENT PRIMARY KEY,
    اسم_المادة TEXT(100) NOT NULL,
    رمز_المادة TEXT(20) UNIQUE NOT NULL,
    المستوى TEXT(20) NOT NULL, -- أول متوسط، ثاني متوسط، ثالث متوسط
    عدد_الحصص_الاسبوعية INTEGER DEFAULT 1,
    نوع_المادة TEXT(30) DEFAULT 'أساسية', -- أساسية، اختيارية، نشاط
    الدرجة_النهائية INTEGER DEFAULT 100,
    درجة_الاعمال INTEGER DEFAULT 40,
    درجة_الاختبار INTEGER DEFAULT 60,
    وصف_المادة MEMO,
    تاريخ_الاضافة DATETIME DEFAULT NOW(),
    حالة_النشاط YESNO DEFAULT TRUE
);

-- جدول الطلاب
CREATE TABLE الطلاب (
    رقم_الطالب AUTOINCREMENT PRIMARY KEY,
    رقم_الهوية TEXT(20) UNIQUE NOT NULL,
    الاسم_الكامل TEXT(100) NOT NULL,
    تاريخ_الميلاد DATETIME NOT NULL,
    مكان_الميلاد TEXT(50),
    الجنسية TEXT(30) DEFAULT 'سعودي',
    رقم_الصف LONG NOT NULL,
    رقم_الجلوس INTEGER,
    اسم_ولي_الامر TEXT(100) NOT NULL,
    رقم_جوال_ولي_الامر TEXT(15) NOT NULL,
    رقم_هوية_ولي_الامر TEXT(20),
    العنوان TEXT(200),
    البريد_الالكتروني TEXT(100),
    حالة_الطالب TEXT(20) DEFAULT 'منتظم', -- منتظم، منقطع، محول
    تاريخ_القيد DATETIME DEFAULT NOW(),
    تاريخ_التخرج DATETIME,
    العام_الدراسي TEXT(20),
    الفصل_الدراسي TEXT(20),
    ملاحظات MEMO,
    صورة_الطالب OLEOBJECT,
    تاريخ_الاضافة DATETIME DEFAULT NOW(),
    حالة_النشاط YESNO DEFAULT TRUE,
    FOREIGN KEY (رقم_الصف) REFERENCES الصفوف(رقم_الصف)
);

-- جدول تدريس المواد (ربط المعلمين بالمواد والصفوف)
CREATE TABLE تدريس_المواد (
    رقم_التدريس AUTOINCREMENT PRIMARY KEY,
    رقم_المعلم LONG NOT NULL,
    رقم_المادة LONG NOT NULL,
    رقم_الصف LONG NOT NULL,
    العام_الدراسي TEXT(20) NOT NULL,
    الفصل_الدراسي TEXT(20) NOT NULL,
    عدد_الحصص_الاسبوعية INTEGER DEFAULT 1,
    تاريخ_البداية DATETIME DEFAULT NOW(),
    تاريخ_النهاية DATETIME,
    ملاحظات MEMO,
    حالة_النشاط YESNO DEFAULT TRUE,
    FOREIGN KEY (رقم_المعلم) REFERENCES المعلمين(رقم_المعلم),
    FOREIGN KEY (رقم_المادة) REFERENCES المواد_الدراسية(رقم_المادة),
    FOREIGN KEY (رقم_الصف) REFERENCES الصفوف(رقم_الصف)
);

-- جدول الحضور والغياب للطلاب
CREATE TABLE حضور_الطلاب (
    رقم_الحضور AUTOINCREMENT PRIMARY KEY,
    رقم_الطالب LONG NOT NULL,
    التاريخ DATETIME NOT NULL,
    حالة_الحضور TEXT(20) DEFAULT 'حاضر', -- حاضر، غائب، متأخر، مستأذن
    وقت_الوصول DATETIME,
    وقت_المغادرة DATETIME,
    سبب_الغياب TEXT(100),
    رقم_المعلم_المسجل LONG,
    ملاحظات MEMO,
    تاريخ_التسجيل DATETIME DEFAULT NOW(),
    FOREIGN KEY (رقم_الطالب) REFERENCES الطلاب(رقم_الطالب),
    FOREIGN KEY (رقم_المعلم_المسجل) REFERENCES المعلمين(رقم_المعلم)
);

-- جدول الحضور والغياب للمعلمين
CREATE TABLE حضور_المعلمين (
    رقم_الحضور AUTOINCREMENT PRIMARY KEY,
    رقم_المعلم LONG NOT NULL,
    التاريخ DATETIME NOT NULL,
    حالة_الحضور TEXT(20) DEFAULT 'حاضر', -- حاضر، غائب، متأخر، إجازة، مأمورية
    وقت_الوصول DATETIME,
    وقت_المغادرة DATETIME,
    نوع_الاجازة TEXT(50),
    سبب_الغياب TEXT(100),
    رقم_المسجل LONG,
    ملاحظات MEMO,
    تاريخ_التسجيل DATETIME DEFAULT NOW(),
    FOREIGN KEY (رقم_المعلم) REFERENCES المعلمين(رقم_المعلم)
);

-- جدول الدرجات
CREATE TABLE الدرجات (
    رقم_الدرجة AUTOINCREMENT PRIMARY KEY,
    رقم_الطالب LONG NOT NULL,
    رقم_المادة LONG NOT NULL,
    نوع_التقييم TEXT(30) NOT NULL, -- اختبار شهري، واجب، مشاركة، اختبار نهائي
    الدرجة DOUBLE NOT NULL,
    الدرجة_النهائية DOUBLE NOT NULL,
    التاريخ DATETIME NOT NULL,
    العام_الدراسي TEXT(20) NOT NULL,
    الفصل_الدراسي TEXT(20) NOT NULL,
    رقم_المعلم_المدخل LONG,
    ملاحظات MEMO,
    تاريخ_الادخال DATETIME DEFAULT NOW(),
    FOREIGN KEY (رقم_الطالب) REFERENCES الطلاب(رقم_الطالب),
    FOREIGN KEY (رقم_المادة) REFERENCES المواد_الدراسية(رقم_المادة),
    FOREIGN KEY (رقم_المعلم_المدخل) REFERENCES المعلمين(رقم_المعلم)
);

-- جدول المخالفات السلوكية
CREATE TABLE المخالفات_السلوكية (
    رقم_المخالفة AUTOINCREMENT PRIMARY KEY,
    رقم_الطالب LONG NOT NULL,
    نوع_المخالفة TEXT(50) NOT NULL,
    وصف_المخالفة MEMO NOT NULL,
    درجة_المخالفة TEXT(20) DEFAULT 'بسيطة', -- بسيطة، متوسطة، كبيرة
    التاريخ DATETIME NOT NULL,
    رقم_المعلم_المبلغ LONG NOT NULL,
    الاجراء_المتخذ MEMO,
    حالة_المخالفة TEXT(20) DEFAULT 'مفتوحة', -- مفتوحة، مغلقة، متابعة
    تاريخ_التسجيل DATETIME DEFAULT NOW(),
    FOREIGN KEY (رقم_الطالب) REFERENCES الطلاب(رقم_الطالب),
    FOREIGN KEY (رقم_المعلم_المبلغ) REFERENCES المعلمين(رقم_المعلم)
);

-- جدول الإشعارات والرسائل
CREATE TABLE الاشعارات (
    رقم_الاشعار AUTOINCREMENT PRIMARY KEY,
    نوع_الاشعار TEXT(30) NOT NULL, -- رسالة، تنبيه، إعلان
    العنوان TEXT(100) NOT NULL,
    المحتوى MEMO NOT NULL,
    المرسل_اليه TEXT(20) NOT NULL, -- طلاب، معلمين، أولياء أمور، الكل
    رقم_المرسل LONG,
    تاريخ_الارسال DATETIME DEFAULT NOW(),
    حالة_الارسال TEXT(20) DEFAULT 'مرسل', -- مرسل، معلق، فشل
    عدد_المستقبلين INTEGER DEFAULT 0,
    ملاحظات MEMO,
    FOREIGN KEY (رقم_المرسل) REFERENCES المعلمين(رقم_المعلم)
);

-- جدول اللجان والاختبارات
CREATE TABLE لجان_الاختبارات (
    رقم_اللجنة AUTOINCREMENT PRIMARY KEY,
    اسم_اللجنة TEXT(50) NOT NULL,
    رقم_القاعة TEXT(20),
    العدد_الاقصى INTEGER DEFAULT 30,
    العدد_الحالي INTEGER DEFAULT 0,
    نوع_الاختبار TEXT(30) NOT NULL, -- شهري، نهائي، دور ثاني
    تاريخ_الاختبار DATETIME NOT NULL,
    وقت_البداية DATETIME,
    وقت_النهاية DATETIME,
    رقم_المراقب_الاول LONG,
    رقم_المراقب_الثاني LONG,
    العام_الدراسي TEXT(20) NOT NULL,
    الفصل_الدراسي TEXT(20) NOT NULL,
    ملاحظات MEMO,
    تاريخ_الانشاء DATETIME DEFAULT NOW(),
    حالة_النشاط YESNO DEFAULT TRUE,
    FOREIGN KEY (رقم_المراقب_الاول) REFERENCES المعلمين(رقم_المعلم),
    FOREIGN KEY (رقم_المراقب_الثاني) REFERENCES المعلمين(رقم_المعلم)
);

-- جدول توزيع الطلاب على اللجان
CREATE TABLE توزيع_اللجان (
    رقم_التوزيع AUTOINCREMENT PRIMARY KEY,
    رقم_الطالب LONG NOT NULL,
    رقم_اللجنة LONG NOT NULL,
    رقم_الجلوس INTEGER NOT NULL,
    رقم_المادة LONG NOT NULL,
    تاريخ_التوزيع DATETIME DEFAULT NOW(),
    ملاحظات MEMO,
    FOREIGN KEY (رقم_الطالب) REFERENCES الطلاب(رقم_الطالب),
    FOREIGN KEY (رقم_اللجنة) REFERENCES لجان_الاختبارات(رقم_اللجنة),
    FOREIGN KEY (رقم_المادة) REFERENCES المواد_الدراسية(رقم_المادة)
);

-- جدول المستخدمين والصلاحيات
CREATE TABLE المستخدمين (
    رقم_المستخدم AUTOINCREMENT PRIMARY KEY,
    اسم_المستخدم TEXT(50) UNIQUE NOT NULL,
    كلمة_المرور TEXT(100) NOT NULL,
    الاسم_الكامل TEXT(100) NOT NULL,
    نوع_المستخدم TEXT(20) NOT NULL, -- مدير، وكيل، معلم، موظف
    الصلاحيات MEMO,
    رقم_المعلم LONG,
    تاريخ_انشاء_الحساب DATETIME DEFAULT NOW(),
    تاريخ_اخر_دخول DATETIME,
    حالة_النشاط YESNO DEFAULT TRUE,
    FOREIGN KEY (رقم_المعلم) REFERENCES المعلمين(رقم_المعلم)
);

-- جدول إعدادات النظام
CREATE TABLE اعدادات_النظام (
    رقم_الاعداد AUTOINCREMENT PRIMARY KEY,
    اسم_الاعداد TEXT(50) UNIQUE NOT NULL,
    قيمة_الاعداد TEXT(200),
    وصف_الاعداد MEMO,
    تاريخ_التحديث DATETIME DEFAULT NOW()
);

-- إدراج البيانات الأساسية
INSERT INTO اعدادات_النظام (اسم_الاعداد, قيمة_الاعداد, وصف_الاعداد) VALUES
('العام_الدراسي_الحالي', '1446-1447', 'العام الدراسي الحالي'),
('الفصل_الدراسي_الحالي', 'الأول', 'الفصل الدراسي الحالي'),
('اسم_النظام', 'نظام إدارة المدرسة المتوسطة', 'اسم النظام'),
('اصدار_النظام', '1.0', 'إصدار النظام'),
('تاريخ_بداية_العام', '2024-08-25', 'تاريخ بداية العام الدراسي'),
('تاريخ_نهاية_العام', '2025-06-30', 'تاريخ نهاية العام الدراسي');

-- إدراج المواد الدراسية الأساسية
INSERT INTO المواد_الدراسية (اسم_المادة, رمز_المادة, المستوى, عدد_الحصص_الاسبوعية, الدرجة_النهائية, درجة_الاعمال, درجة_الاختبار) VALUES
('القرآن الكريم', 'QUR', 'أول متوسط', 2, 100, 40, 60),
('التفسير', 'TAF', 'أول متوسط', 1, 100, 40, 60),
('الحديث', 'HAD', 'أول متوسط', 1, 100, 40, 60),
('الفقه', 'FIQ', 'أول متوسط', 1, 100, 40, 60),
('التوحيد', 'TOH', 'أول متوسط', 1, 100, 40, 60),
('اللغة العربية', 'ARB', 'أول متوسط', 6, 100, 40, 60),
('الرياضيات', 'MAT', 'أول متوسط', 5, 100, 40, 60),
('العلوم', 'SCI', 'أول متوسط', 4, 100, 40, 60),
('الدراسات الاجتماعية', 'SOC', 'أول متوسط', 3, 100, 40, 60),
('اللغة الإنجليزية', 'ENG', 'أول متوسط', 4, 100, 40, 60),
('التربية الفنية', 'ART', 'أول متوسط', 1, 100, 60, 40),
('التربية البدنية', 'PHY', 'أول متوسط', 2, 100, 80, 20),
('الحاسب الآلي', 'COM', 'أول متوسط', 1, 100, 60, 40);

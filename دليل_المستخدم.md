# دليل المستخدم - نظام إدارة المدرسة المتوسطة

## نظرة عامة

نظام إدارة المدرسة المتوسطة هو برنامج شامل مصمم خصيصاً للمدارس المتوسطة في المملكة العربية السعودية. يوفر البرنامج جميع الأدوات اللازمة لإدارة العمليات اليومية للمدرسة بكفاءة عالية.

## المميزات الرئيسية

### 🎓 إدارة الطلاب
- إضافة وتعديل وحذف بيانات الطلاب
- استيراد بيانات الطلاب من نظام نور
- إدارة الصفوف والشعب
- متابعة حالة الطلاب الأكاديمية

### 👨‍🏫 إدارة المعلمين
- إدارة بيانات المعلمين والموظفين
- تخصيص المواد والصفوف للمعلمين
- متابعة حضور وغياب المعلمين
- إدارة الصلاحيات والمستخدمين

### 📊 الحضور والغياب
- تسجيل حضور وغياب الطلاب يومياً
- متابعة تأخر الطلاب والمعلمين
- إنتاج تقارير الحضور والغياب
- إرسال إشعارات الغياب لأولياء الأمور

### 📈 الدرجات والتقييم
- إدخال درجات الطلاب لجميع المواد
- دعم أنواع التقييم المختلفة (شهري، نهائي، أعمال السنة)
- حساب النسب المئوية والمعدلات
- إنتاج الشهادات والكشوف

### ⚠️ المخالفات السلوكية
- تسجيل المخالفات السلوكية للطلاب
- تصنيف المخالفات حسب الدرجة
- متابعة الإجراءات المتخذة
- إرسال إشعارات لأولياء الأمور

### 📋 إدارة الاختبارات
- إنشاء لجان الاختبارات
- توزيع الطلاب على اللجان
- إنتاج أرقام الجلوس
- طباعة كشوف اللجان

### 📱 الرسائل والإشعارات
- إرسال رسائل جماعية لأولياء الأمور
- إشعارات الدرجات والمخالفات
- رسائل الحضور والغياب
- إعلانات المدرسة

### 📊 التقارير والإحصائيات
- تقارير شاملة للطلاب والمعلمين
- إحصائيات الحضور والغياب
- تقارير الأداء الأكاديمي
- قوائم الطلاب المتفوقين

## متطلبات النظام

### الحد الأدنى
- نظام التشغيل: Windows 10 أو أحدث
- المعالج: Intel Core i3 أو معادل
- الذاكرة: 4 جيجابايت RAM
- مساحة القرص الصلب: 2 جيجابايت
- Microsoft Access 2016 أو أحدث

### المستحسن
- نظام التشغيل: Windows 11
- المعالج: Intel Core i5 أو أحدث
- الذاكرة: 8 جيجابايت RAM
- مساحة القرص الصلب: 5 جيجابايت
- Microsoft Office 365

## التركيب والإعداد

### خطوات التركيب

1. **تحميل الملفات**
   - قم بتحميل ملف `نظام_إدارة_المدرسة_المتوسطة.accdb`
   - تأكد من وجود Microsoft Access على الجهاز

2. **إعداد قاعدة البيانات**
   - افتح ملف قاعدة البيانات
   - قم بتشغيل استعلامات الإنشاء
   - أدخل البيانات الأساسية للمدرسة

3. **إعداد المستخدمين**
   - أنشئ حساب المدير الرئيسي
   - حدد الصلاحيات للمستخدمين
   - اختبر تسجيل الدخول

### الإعداد الأولي

1. **معلومات المدرسة**
   ```
   - اسم المدرسة
   - رمز المدرسة
   - العنوان ومعلومات الاتصال
   - اسم المدير والوكيل
   ```

2. **العام الدراسي**
   ```
   - العام الدراسي الحالي
   - الفصل الدراسي الحالي
   - تواريخ بداية ونهاية العام
   ```

3. **الصفوف والمواد**
   ```
   - إنشاء الصفوف الدراسية
   - إضافة المواد الدراسية
   - تحديد الدرجات النهائية
   ```

## دليل الاستخدام

### تسجيل الدخول

1. افتح البرنامج
2. أدخل اسم المستخدم وكلمة المرور
3. اختر نوع المستخدم (مدير/وكيل/معلم)
4. انقر على "دخول"

### الواجهة الرئيسية

الواجهة الرئيسية تحتوي على:
- **الإحصائيات السريعة**: عدد الطلاب، المعلمين، الصفوف، المواد
- **القائمة الرئيسية**: أيقونات للوصول السريع لجميع الوظائف
- **معلومات المستخدم**: اسم المستخدم الحالي ونوع الحساب

### إدارة الطلاب

#### إضافة طالب جديد
1. انقر على "إدارة الطلاب"
2. اختر "إضافة طالب جديد"
3. املأ البيانات المطلوبة:
   - رقم الهوية
   - الاسم الكامل
   - تاريخ الميلاد
   - الصف
   - بيانات ولي الأمر
4. انقر على "حفظ"

#### تعديل بيانات طالب
1. ابحث عن الطالب في القائمة
2. انقر على "تعديل"
3. عدل البيانات المطلوبة
4. انقر على "حفظ التغييرات"

#### استيراد من نور
1. انقر على "استيراد من نور"
2. اختر نوع الملف (Excel أو CSV)
3. حدد مسار الملف
4. تأكد من تطابق الأعمدة
5. انقر على "استيراد"

### تسجيل الحضور والغياب

#### الحضور اليومي
1. انقر على "الحضور والغياب"
2. اختر الصف والتاريخ
3. حدد المعلم المسجل
4. انقر على "تسجيل حضور جماعي"
5. عدل حالة الطلاب الغائبين أو المتأخرين

#### تسجيل غياب فردي
1. اختر الطالب من القائمة
2. انقر على "تسجيل غياب"
3. أدخل سبب الغياب
4. انقر على "حفظ"

### إدخال الدرجات

#### إدخال درجات صف كامل
1. انقر على "الدرجات والتقييم"
2. اختر الصف والمادة
3. حدد نوع التقييم
4. أدخل الدرجات للطلاب
5. انقر على "حفظ الدرجات"

#### مراجعة الدرجات
1. اختر الطالب أو الصف
2. حدد المادة والفترة
3. راجع الدرجات المدخلة
4. عدل إذا لزم الأمر

### المخالفات السلوكية

#### تسجيل مخالفة
1. انقر على "المخالفات السلوكية"
2. اختر الطالب
3. حدد نوع المخالفة
4. أدخل وصف المخالفة
5. حدد درجة المخالفة
6. انقر على "حفظ"

### إدارة الاختبارات

#### إنشاء لجان الاختبارات
1. انقر على "إدارة الاختبارات"
2. اختر "إنشاء لجان جديدة"
3. حدد نوع الاختبار وتاريخه
4. أدخل عدد الطلاب في كل لجنة
5. انقر على "إنشاء اللجان"

#### توزيع الطلاب
1. سيتم توزيع الطلاب تلقائياً
2. راجع التوزيع
3. عدل إذا لزم الأمر
4. اطبع كشوف اللجان

### التقارير

#### تقرير الحضور
1. انقر على "التقارير"
2. اختر "تقرير الحضور"
3. حدد الصف والفترة الزمنية
4. انقر على "إنتاج التقرير"
5. اطبع أو احفظ التقرير

#### شهادة طالب
1. اختر "شهادة الطالب"
2. حدد الطالب والفصل الدراسي
3. انقر على "إنتاج الشهادة"
4. راجع البيانات واطبع

## الرسائل والإشعارات

### إرسال رسالة جماعية
1. انقر على "الرسائل والإشعارات"
2. اختر "رسالة جماعية"
3. حدد المستقبلين (طلاب/معلمين/أولياء أمور)
4. أدخل عنوان ومحتوى الرسالة
5. انقر على "إرسال"

### إشعارات تلقائية
- إشعارات الغياب ترسل تلقائياً
- إشعارات المخالفات ترسل فور التسجيل
- إشعارات الدرجات يمكن إرسالها بعد إدخال النتائج

## النسخ الاحتياطي

### إنشاء نسخة احتياطية
1. انقر على "الإعدادات"
2. اختر "النسخ الاحتياطي"
3. انقر على "إنشاء نسخة احتياطية"
4. حدد مكان الحفظ
5. انقر على "حفظ"

### استعادة نسخة احتياطية
1. انقر على "استعادة نسخة احتياطية"
2. اختر ملف النسخة الاحتياطية
3. تأكد من الاستعادة
4. أعد تشغيل البرنامج

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### لا يمكن فتح قاعدة البيانات
- تأكد من وجود Microsoft Access
- تحقق من صلاحيات الملف
- أعد تشغيل البرنامج كمدير

#### بطء في الأداء
- أغلق البرامج غير المستخدمة
- تأكد من توفر مساحة كافية على القرص
- قم بضغط قاعدة البيانات

#### خطأ في استيراد البيانات
- تحقق من تنسيق الملف
- تأكد من تطابق الأعمدة
- احذف الصفوف الفارغة

## الدعم الفني

### طرق التواصل
- البريد الإلكتروني: <EMAIL>
- الهاتف: 920000000
- الواتساب: 966500000000

### ساعات العمل
- الأحد إلى الخميس: 8:00 ص - 5:00 م
- الجمعة والسبت: مغلق

## التحديثات

### كيفية التحديث
1. تحميل الإصدار الجديد
2. إنشاء نسخة احتياطية
3. تركيب التحديث
4. اختبار الوظائف

### سجل التحديثات
- الإصدار 1.0: الإصدار الأولي
- التحديثات القادمة ستشمل مميزات إضافية

## الخاتمة

نظام إدارة المدرسة المتوسطة يوفر حلاً شاملاً لجميع احتياجات المدرسة. للحصول على أفضل النتائج، ننصح بالتدريب المناسب لجميع المستخدمين واتباع الإرشادات الواردة في هذا الدليل.

---

**© 2024 نظام إدارة المدرسة المتوسطة - جميع الحقوق محفوظة**

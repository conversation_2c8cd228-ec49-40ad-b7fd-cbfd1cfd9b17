# نظام إدارة المدرسة المتوسطة

## 🏫 نظرة عامة

نظام إدارة المدرسة المتوسطة هو برنامج شامل مطور خصيصاً للمدارس المتوسطة في المملكة العربية السعودية. يوفر النظام جميع الأدوات اللازمة لإدارة العمليات اليومية للمدرسة بكفاءة عالية، مع التكامل الكامل مع نظام نور الحكومي.

## ✨ المميزات الرئيسية

### 🎓 إدارة شاملة للطلاب
- إضافة وتعديل وحذف بيانات الطلاب
- استيراد البيانات من نظام نور (Excel, CSV)
- إدارة الصفوف والشعب الدراسية
- متابعة الحالة الأكاديمية للطلاب

### 👨‍🏫 إدارة المعلمين والموظفين
- إدارة بيانات المعلمين والموظفين
- تخصيص المواد والصفوف للمعلمين
- نظام الصلاحيات والمستخدمين
- متابعة حضور وغياب المعلمين

### 📊 نظام الحضور والغياب
- تسجيل حضور وغياب الطلاب يومياً
- متابعة التأخير والاستئذان
- إنتاج تقارير الحضور والغياب
- إرسال إشعارات تلقائية لأولياء الأمور

### 📈 إدارة الدرجات والتقييم
- إدخال درجات الطلاب لجميع المواد
- دعم أنواع التقييم المختلفة (شهري، نهائي، أعمال السنة)
- حساب النسب المئوية والمعدلات تلقائياً
- إنتاج الشهادات والكشوف

### ⚠️ نظام المخالفات السلوكية
- تسجيل ومتابعة المخالفات السلوكية
- تصنيف المخالفات حسب الدرجة
- متابعة الإجراءات المتخذة
- إرسال إشعارات فورية لأولياء الأمور

### 📋 إدارة الاختبارات
- إنشاء لجان الاختبارات تلقائياً
- توزيع الطلاب على اللجان
- إنتاج أرقام الجلوس
- طباعة كشوف اللجان والمراقبين

### 📱 نظام الرسائل والإشعارات
- إرسال رسائل جماعية لأولياء الأمور
- إشعارات الدرجات والمخالفات
- رسائل الحضور والغياب
- إعلانات المدرسة العامة

### 📊 التقارير والإحصائيات
- تقارير شاملة للطلاب والمعلمين
- إحصائيات الحضور والأداء
- قوائم الطلاب المتفوقين
- تقارير إدارية مفصلة

## 🛠️ التقنيات المستخدمة

- **قاعدة البيانات**: Microsoft Access
- **البرمجة**: VBA (Visual Basic for Applications)
- **الواجهة**: HTML5, CSS3, JavaScript
- **التصميم**: Bootstrap 5, Font Awesome
- **التوافق**: نظام نور الحكومي

## 📋 متطلبات النظام

### الحد الأدنى
- **نظام التشغيل**: Windows 10 أو أحدث
- **المعالج**: Intel Core i3 أو معادل
- **الذاكرة**: 4 جيجابايت RAM
- **مساحة القرص**: 2 جيجابايت
- **البرامج**: Microsoft Access 2016 أو أحدث

### المستحسن
- **نظام التشغيل**: Windows 11
- **المعالج**: Intel Core i5 أو أحدث
- **الذاكرة**: 8 جيجابايت RAM
- **مساحة القرص**: 5 جيجابايت
- **البرامج**: Microsoft Office 365

## 🚀 التركيب والإعداد

### 1. تحميل الملفات
```bash
# قم بتحميل الملفات التالية:
- نظام_إدارة_المدرسة_المتوسطة.accdb
- نماذج_النظام.vba
- الواجهة_الرئيسية.html
- تقارير_النظام.sql
- دليل_المستخدم.md
```

### 2. إعداد قاعدة البيانات
1. افتح ملف `نظام_إدارة_المدرسة_المتوسطة.accdb`
2. قم بتشغيل استعلامات الإنشاء
3. أدخل البيانات الأساسية للمدرسة

### 3. إعداد النماذج والتقارير
1. استورد ملف `نماذج_النظام.vba`
2. قم بتشغيل ملف `تقارير_النظام.sql`
3. اختبر جميع الوظائف

### 4. الإعداد الأولي
1. أدخل معلومات المدرسة الأساسية
2. أنشئ حسابات المستخدمين
3. أضف الصفوف والمواد الدراسية
4. اختبر النظام

## 📖 دليل الاستخدام السريع

### تسجيل الدخول
1. افتح البرنامج
2. أدخل اسم المستخدم وكلمة المرور
3. اختر نوع المستخدم
4. انقر على "دخول"

### إضافة طالب جديد
1. انقر على "إدارة الطلاب"
2. اختر "إضافة طالب جديد"
3. املأ البيانات المطلوبة
4. انقر على "حفظ"

### استيراد من نور
1. انقر على "استيراد من نور"
2. اختر نوع الملف (Excel/CSV)
3. حدد مسار الملف
4. انقر على "استيراد"

### تسجيل الحضور
1. انقر على "الحضور والغياب"
2. اختر الصف والتاريخ
3. انقر على "تسجيل حضور جماعي"
4. عدل حالة الطلاب الغائبين

## 📁 هيكل المشروع

```
نظام-إدارة-المدرسة-المتوسطة/
├── نظام_إدارة_المدرسة_المتوسطة.accdb    # قاعدة البيانات الرئيسية
├── نماذج_النظام.vba                        # كود VBA للنماذج والوظائف
├── الواجهة_الرئيسية.html                   # الواجهة الرئيسية للنظام
├── تقارير_النظام.sql                       # استعلامات التقارير
├── دليل_المستخدم.md                        # دليل المستخدم الشامل
└── README.md                               # ملف التوثيق الرئيسي
```

## 🔧 الوظائف المتقدمة

### استيراد البيانات
- دعم ملفات Excel و CSV
- تحقق تلقائي من صحة البيانات
- تجنب التكرار في البيانات
- تقارير الاستيراد المفصلة

### النسخ الاحتياطي
- إنشاء نسخ احتياطية تلقائية
- استعادة البيانات بسهولة
- جدولة النسخ الاحتياطي
- ضغط الملفات لتوفير المساحة

### الأمان والصلاحيات
- نظام مستخدمين متعدد المستويات
- تشفير كلمات المرور
- تسجيل العمليات والأنشطة
- صلاحيات مخصصة لكل مستخدم

## 📊 لقطات الشاشة

### الواجهة الرئيسية
![الواجهة الرئيسية](screenshots/main-interface.png)

### إدارة الطلاب
![إدارة الطلاب](screenshots/student-management.png)

### نظام الدرجات
![نظام الدرجات](screenshots/grades-system.png)

## 🤝 المساهمة

نرحب بالمساهمات لتطوير النظام! يمكنك المساهمة من خلال:

1. **الإبلاغ عن الأخطاء**: استخدم نظام Issues
2. **اقتراح مميزات جديدة**: أرسل Feature Request
3. **تحسين الكود**: أرسل Pull Request
4. **تحسين التوثيق**: ساعد في تطوير الدليل

### خطوات المساهمة
1. Fork المشروع
2. أنشئ branch جديد (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push إلى Branch (`git push origin feature/AmazingFeature`)
5. افتح Pull Request

## 📝 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم والتواصل

### الدعم الفني
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: 920000000
- **الواتساب**: 966500000000

### ساعات العمل
- **الأحد إلى الخميس**: 8:00 ص - 5:00 م
- **الجمعة والسبت**: مغلق

### روابط مفيدة
- [دليل المستخدم الكامل](دليل_المستخدم.md)
- [الأسئلة الشائعة](FAQ.md)
- [فيديوهات التدريب](https://youtube.com/school-system)
- [المنتدى](https://forum.school-system.sa)

## 🏆 الشكر والتقدير

نتقدم بالشكر لجميع من ساهم في تطوير هذا النظام:
- فريق التطوير المحلي
- المدارس التي ساعدت في الاختبار
- المجتمع التقني السعودي

## 📈 خارطة الطريق

### الإصدار 1.1 (قريباً)
- [ ] تطبيق جوال للمعلمين
- [ ] تكامل مع الواتساب
- [ ] تقارير تفاعلية
- [ ] نظام الإنذارات المبكرة

### الإصدار 1.2 (المستقبل)
- [ ] ذكاء اصطناعي للتنبؤ بالأداء
- [ ] تطبيق لأولياء الأمور
- [ ] نظام المكتبة الرقمية
- [ ] تكامل مع منصة مدرستي

## 🔄 سجل التحديثات

### الإصدار 1.0.0 (2024-12-12)
- ✅ الإصدار الأولي
- ✅ جميع الوظائف الأساسية
- ✅ التكامل مع نظام نور
- ✅ واجهة المستخدم العربية
- ✅ نظام التقارير الشامل

---

**© 2024 نظام إدارة المدرسة المتوسطة - جميع الحقوق محفوظة**

**صنع بـ ❤️ في المملكة العربية السعودية**

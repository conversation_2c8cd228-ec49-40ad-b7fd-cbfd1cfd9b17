<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المدرسة المتوسطة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 20px;
            padding: 30px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border-radius: 15px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: bold;
        }
        
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .stats-row {
            margin-bottom: 40px;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-left: 5px solid;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }
        
        .stat-card.students { border-left-color: #3498db; }
        .stat-card.teachers { border-left-color: #e74c3c; }
        .stat-card.classes { border-left-color: #f39c12; }
        .stat-card.subjects { border-left-color: #9b59b6; }
        
        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 1.1rem;
            color: #666;
            font-weight: 500;
        }
        
        .menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .menu-item {
            background: white;
            border-radius: 15px;
            padding: 30px 20px;
            text-align: center;
            text-decoration: none;
            color: #333;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .menu-item:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            color: #333;
            border-color: #4CAF50;
        }
        
        .menu-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            display: block;
        }
        
        .menu-title {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .menu-desc {
            font-size: 0.9rem;
            color: #666;
            line-height: 1.4;
        }
        
        .students-icon { color: #3498db; }
        .teachers-icon { color: #e74c3c; }
        .attendance-icon { color: #2ecc71; }
        .grades-icon { color: #f39c12; }
        .violations-icon { color: #e67e22; }
        .reports-icon { color: #9b59b6; }
        .exams-icon { color: #34495e; }
        .messages-icon { color: #1abc9c; }
        .import-icon { color: #16a085; }
        .settings-icon { color: #7f8c8d; }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            color: #666;
            border-top: 1px solid #eee;
        }
        
        .user-info {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px 15px;
            border-radius: 10px;
            font-size: 0.9rem;
        }
        
        .logout-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: #e74c3c;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 10px;
            text-decoration: none;
            transition: background 0.3s ease;
        }
        
        .logout-btn:hover {
            background: #c0392b;
            color: white;
        }
        
        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .menu-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
            }
            
            .stat-number {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="user-info">
        <i class="fas fa-user"></i> مرحباً، المدير
    </div>
    
    <a href="#" class="logout-btn" onclick="logout()">
        <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
    </a>
    
    <div class="container-fluid">
        <div class="main-container">
            <div class="header">
                <h1><i class="fas fa-school"></i> نظام إدارة المدرسة المتوسطة</h1>
                <p>الإصدار 1.0 - مدرسة الأمل المتوسطة</p>
            </div>
            
            <!-- إحصائيات سريعة -->
            <div class="row stats-row">
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="stat-card students">
                        <div class="stat-number" id="studentsCount">450</div>
                        <div class="stat-label">إجمالي الطلاب</div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="stat-card teachers">
                        <div class="stat-number" id="teachersCount">35</div>
                        <div class="stat-label">إجمالي المعلمين</div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="stat-card classes">
                        <div class="stat-number" id="classesCount">15</div>
                        <div class="stat-label">إجمالي الصفوف</div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="stat-card subjects">
                        <div class="stat-number" id="subjectsCount">13</div>
                        <div class="stat-label">إجمالي المواد</div>
                    </div>
                </div>
            </div>
            
            <!-- القائمة الرئيسية -->
            <div class="menu-grid">
                <a href="#" class="menu-item" onclick="openModule('students')">
                    <i class="fas fa-user-graduate menu-icon students-icon"></i>
                    <div class="menu-title">إدارة الطلاب</div>
                    <div class="menu-desc">إضافة وتعديل وحذف بيانات الطلاب</div>
                </a>
                
                <a href="#" class="menu-item" onclick="openModule('teachers')">
                    <i class="fas fa-chalkboard-teacher menu-icon teachers-icon"></i>
                    <div class="menu-title">إدارة المعلمين</div>
                    <div class="menu-desc">إدارة بيانات المعلمين والموظفين</div>
                </a>
                
                <a href="#" class="menu-item" onclick="openModule('attendance')">
                    <i class="fas fa-calendar-check menu-icon attendance-icon"></i>
                    <div class="menu-title">الحضور والغياب</div>
                    <div class="menu-desc">تسجيل ومتابعة حضور الطلاب والمعلمين</div>
                </a>
                
                <a href="#" class="menu-item" onclick="openModule('grades')">
                    <i class="fas fa-chart-line menu-icon grades-icon"></i>
                    <div class="menu-title">الدرجات والتقييم</div>
                    <div class="menu-desc">إدخال ومتابعة درجات الطلاب</div>
                </a>
                
                <a href="#" class="menu-item" onclick="openModule('violations')">
                    <i class="fas fa-exclamation-triangle menu-icon violations-icon"></i>
                    <div class="menu-title">المخالفات السلوكية</div>
                    <div class="menu-desc">تسجيل ومتابعة المخالفات السلوكية</div>
                </a>
                
                <a href="#" class="menu-item" onclick="openModule('reports')">
                    <i class="fas fa-file-alt menu-icon reports-icon"></i>
                    <div class="menu-title">التقارير</div>
                    <div class="menu-desc">إنتاج التقارير والإحصائيات</div>
                </a>
                
                <a href="#" class="menu-item" onclick="openModule('exams')">
                    <i class="fas fa-clipboard-list menu-icon exams-icon"></i>
                    <div class="menu-title">إدارة الاختبارات</div>
                    <div class="menu-desc">إعداد اللجان وأرقام الجلوس</div>
                </a>
                
                <a href="#" class="menu-item" onclick="openModule('messages')">
                    <i class="fas fa-envelope menu-icon messages-icon"></i>
                    <div class="menu-title">الرسائل والإشعارات</div>
                    <div class="menu-desc">إرسال الرسائل لأولياء الأمور</div>
                </a>
                
                <a href="#" class="menu-item" onclick="openModule('import')">
                    <i class="fas fa-download menu-icon import-icon"></i>
                    <div class="menu-title">استيراد من نور</div>
                    <div class="menu-desc">استيراد البيانات من نظام نور</div>
                </a>
                
                <a href="#" class="menu-item" onclick="openModule('settings')">
                    <i class="fas fa-cog menu-icon settings-icon"></i>
                    <div class="menu-title">الإعدادات</div>
                    <div class="menu-desc">إعدادات النظام والمدرسة</div>
                </a>
            </div>
            
            <div class="footer">
                <p>&copy; 2024 نظام إدارة المدرسة المتوسطة - جميع الحقوق محفوظة</p>
                <p>تم التطوير بواسطة فريق التطوير المحلي</p>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديث الإحصائيات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateStats();
            setInterval(updateStats, 30000); // تحديث كل 30 ثانية
        });
        
        function updateStats() {
            // هنا يمكن إضافة استدعاءات AJAX لتحديث الإحصائيات من قاعدة البيانات
            // مؤقتاً سنستخدم قيم ثابتة
            
            animateNumber('studentsCount', 450);
            animateNumber('teachersCount', 35);
            animateNumber('classesCount', 15);
            animateNumber('subjectsCount', 13);
        }
        
        function animateNumber(elementId, targetNumber) {
            const element = document.getElementById(elementId);
            const currentNumber = parseInt(element.textContent);
            
            if (currentNumber !== targetNumber) {
                const increment = targetNumber > currentNumber ? 1 : -1;
                const timer = setInterval(() => {
                    const current = parseInt(element.textContent);
                    if (current === targetNumber) {
                        clearInterval(timer);
                    } else {
                        element.textContent = current + increment;
                    }
                }, 50);
            }
        }
        
        function openModule(moduleName) {
            // هنا يتم فتح النموذج المطلوب
            switch(moduleName) {
                case 'students':
                    alert('سيتم فتح نموذج إدارة الطلاب');
                    // DoCmd.OpenForm "نموذج_الطلاب"
                    break;
                case 'teachers':
                    alert('سيتم فتح نموذج إدارة المعلمين');
                    // DoCmd.OpenForm "نموذج_المعلمين"
                    break;
                case 'attendance':
                    alert('سيتم فتح نموذج الحضور والغياب');
                    // DoCmd.OpenForm "نموذج_الحضور"
                    break;
                case 'grades':
                    alert('سيتم فتح نموذج الدرجات');
                    // DoCmd.OpenForm "نموذج_الدرجات"
                    break;
                case 'violations':
                    alert('سيتم فتح نموذج المخالفات السلوكية');
                    // DoCmd.OpenForm "نموذج_المخالفات"
                    break;
                case 'reports':
                    alert('سيتم فتح نموذج التقارير');
                    // DoCmd.OpenForm "نموذج_التقارير"
                    break;
                case 'exams':
                    alert('سيتم فتح نموذج إدارة الاختبارات');
                    // DoCmd.OpenForm "نموذج_الاختبارات"
                    break;
                case 'messages':
                    alert('سيتم فتح نموذج الرسائل والإشعارات');
                    // DoCmd.OpenForm "نموذج_الرسائل"
                    break;
                case 'import':
                    alert('سيتم فتح نموذج استيراد البيانات من نور');
                    // DoCmd.OpenForm "نموذج_الاستيراد"
                    break;
                case 'settings':
                    alert('سيتم فتح نموذج الإعدادات');
                    // DoCmd.OpenForm "نموذج_الاعدادات"
                    break;
                default:
                    alert('الوحدة غير متوفرة حالياً');
            }
        }
        
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                // إعادة توجيه إلى صفحة تسجيل الدخول
                window.location.href = 'login.html';
            }
        }
        
        // إضافة تأثيرات بصرية
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });
            
            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
        
        // إضافة تأثير النقر
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', function(e) {
                // تأثير النقر
                this.style.transform = 'translateY(-5px) scale(0.98)';
                setTimeout(() => {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                }, 150);
            });
        });
    </script>
</body>
</html>

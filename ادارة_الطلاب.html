<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الطلاب - نظام إدارة المدرسة المتوسطة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .main-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin: 20px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: bold;
        }
        
        .header .breadcrumb {
            background: transparent;
            margin: 0;
            padding: 0;
        }
        
        .header .breadcrumb-item a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
        }
        
        .header .breadcrumb-item.active {
            color: white;
        }
        
        .content {
            padding: 30px;
        }
        
        .stats-row {
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease;
            border-left: 4px solid;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card.total { border-left-color: #3498db; }
        .stat-card.active { border-left-color: #27ae60; }
        .stat-card.graduated { border-left-color: #f39c12; }
        .stat-card.transferred { border-left-color: #e74c3c; }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .action-buttons {
            margin-bottom: 25px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn-action {
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #3498db, #2980b9);
            border: none;
        }
        
        .btn-success {
            background: linear-gradient(45deg, #27ae60, #229954);
            border: none;
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #f39c12, #e67e22);
            border: none;
        }
        
        .btn-info {
            background: linear-gradient(45deg, #17a2b8, #138496);
            border: none;
        }
        
        .search-filters {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
        }
        
        .table-container {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .table {
            margin: 0;
        }
        
        .table thead th {
            background: #f8f9fa;
            border: none;
            font-weight: 600;
            color: #495057;
            padding: 15px 10px;
        }
        
        .table tbody td {
            padding: 12px 10px;
            vertical-align: middle;
            border-top: 1px solid #dee2e6;
        }
        
        .student-photo {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #dee2e6;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-transferred {
            background: #fff3cd;
            color: #856404;
        }
        
        .action-buttons-table {
            display: flex;
            gap: 5px;
        }
        
        .btn-sm {
            padding: 5px 10px;
            font-size: 0.8rem;
            border-radius: 6px;
        }
        
        .modal-header {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border-radius: 15px 15px 0 0;
        }
        
        .modal-content {
            border-radius: 15px;
            border: none;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            padding: 10px 15px;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }
        
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }
        
        .required {
            color: #e74c3c;
        }
        
        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
            }
            
            .content {
                padding: 20px;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .btn-action {
                width: 100%;
                margin-bottom: 10px;
            }
            
            .table-responsive {
                font-size: 0.9rem;
            }
        }
        
        .import-area {
            border: 2px dashed #dee2e6;
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .import-area:hover {
            border-color: #3498db;
            background: rgba(52, 152, 219, 0.05);
        }
        
        .import-area.dragover {
            border-color: #27ae60;
            background: rgba(39, 174, 96, 0.1);
        }
        
        .progress {
            height: 8px;
            border-radius: 4px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="header">
            <div>
                <h1><i class="fas fa-user-graduate"></i> إدارة الطلاب</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="الواجهة_الرئيسية.html">الرئيسية</a></li>
                        <li class="breadcrumb-item active">إدارة الطلاب</li>
                    </ol>
                </nav>
            </div>
            <div>
                <button class="btn btn-light btn-sm" onclick="goBack()">
                    <i class="fas fa-arrow-right"></i> العودة
                </button>
            </div>
        </div>
        
        <!-- Content -->
        <div class="content">
            <!-- Statistics -->
            <div class="row stats-row">
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="stat-card total">
                        <div class="stat-number" id="totalStudents">450</div>
                        <div class="stat-label">إجمالي الطلاب</div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="stat-card active">
                        <div class="stat-number" id="activeStudents">425</div>
                        <div class="stat-label">الطلاب النشطون</div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="stat-card graduated">
                        <div class="stat-number" id="graduatedStudents">15</div>
                        <div class="stat-label">المتخرجون</div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="stat-card transferred">
                        <div class="stat-number" id="transferredStudents">10</div>
                        <div class="stat-label">المحولون</div>
                    </div>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="action-buttons">
                <button class="btn btn-primary btn-action" onclick="addStudent()">
                    <i class="fas fa-plus"></i> إضافة طالب جديد
                </button>
                <button class="btn btn-success btn-action" onclick="importStudents()">
                    <i class="fas fa-file-import"></i> استيراد من نور
                </button>
                <button class="btn btn-warning btn-action" onclick="exportStudents()">
                    <i class="fas fa-file-export"></i> تصدير البيانات
                </button>
                <button class="btn btn-info btn-action" onclick="printList()">
                    <i class="fas fa-print"></i> طباعة القائمة
                </button>
            </div>
            
            <!-- Search and Filters -->
            <div class="search-filters">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label class="form-label">البحث</label>
                        <input type="text" class="form-control" id="searchInput" placeholder="ابحث بالاسم أو رقم الهوية">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label class="form-label">الصف</label>
                        <select class="form-select" id="classFilter">
                            <option value="">جميع الصفوف</option>
                            <option value="1">أول متوسط أ</option>
                            <option value="2">أول متوسط ب</option>
                            <option value="3">ثاني متوسط أ</option>
                            <option value="4">ثاني متوسط ب</option>
                            <option value="5">ثالث متوسط أ</option>
                            <option value="6">ثالث متوسط ب</option>
                        </select>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label class="form-label">الحالة</label>
                        <select class="form-select" id="statusFilter">
                            <option value="">جميع الحالات</option>
                            <option value="منتظم">منتظم</option>
                            <option value="محول">محول</option>
                            <option value="منقطع">منقطع</option>
                        </select>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label class="form-label">العام الدراسي</label>
                        <select class="form-select" id="yearFilter">
                            <option value="">جميع الأعوام</option>
                            <option value="1446-1447" selected>1446-1447</option>
                            <option value="1445-1446">1445-1446</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-flex gap-2">
                            <button class="btn btn-primary" onclick="applyFilters()">
                                <i class="fas fa-search"></i> بحث
                            </button>
                            <button class="btn btn-secondary" onclick="clearFilters()">
                                <i class="fas fa-times"></i> مسح
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Students Table -->
            <div class="table-container">
                <div class="table-responsive">
                    <table class="table table-hover" id="studentsTable">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>رقم الطالب</th>
                                <th>الاسم الكامل</th>
                                <th>رقم الهوية</th>
                                <th>الصف</th>
                                <th>تاريخ الميلاد</th>
                                <th>ولي الأمر</th>
                                <th>رقم الجوال</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="studentsTableBody">
                            <!-- سيتم ملء البيانات بواسطة JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal إضافة/تعديل طالب -->
    <div class="modal fade" id="studentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="studentModalTitle">إضافة طالب جديد</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="studentForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم الهوية <span class="required">*</span></label>
                                <input type="text" class="form-control" id="studentId" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الاسم الكامل <span class="required">*</span></label>
                                <input type="text" class="form-control" id="studentName" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">تاريخ الميلاد <span class="required">*</span></label>
                                <input type="date" class="form-control" id="birthDate" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">مكان الميلاد</label>
                                <input type="text" class="form-control" id="birthPlace">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الجنسية</label>
                                <select class="form-select" id="nationality">
                                    <option value="سعودي" selected>سعودي</option>
                                    <option value="مقيم">مقيم</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الصف <span class="required">*</span></label>
                                <select class="form-select" id="studentClass" required>
                                    <option value="">اختر الصف</option>
                                    <option value="1">أول متوسط أ</option>
                                    <option value="2">أول متوسط ب</option>
                                    <option value="3">ثاني متوسط أ</option>
                                    <option value="4">ثاني متوسط ب</option>
                                    <option value="5">ثالث متوسط أ</option>
                                    <option value="6">ثالث متوسط ب</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اسم ولي الأمر <span class="required">*</span></label>
                                <input type="text" class="form-control" id="guardianName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم جوال ولي الأمر <span class="required">*</span></label>
                                <input type="tel" class="form-control" id="guardianPhone" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم هوية ولي الأمر</label>
                                <input type="text" class="form-control" id="guardianId">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">العنوان</label>
                            <textarea class="form-control" id="address" rows="3"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">حالة الطالب</label>
                                <select class="form-select" id="studentStatus">
                                    <option value="منتظم" selected>منتظم</option>
                                    <option value="محول">محول</option>
                                    <option value="منقطع">منقطع</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">صورة الطالب</label>
                                <input type="file" class="form-control" id="studentPhoto" accept="image/*">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="notes" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveStudent()">حفظ</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal استيراد البيانات -->
    <div class="modal fade" id="importModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">استيراد بيانات الطلاب من نور</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="import-area" id="importArea">
                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                        <h5>اسحب وأفلت الملف هنا أو انقر للاختيار</h5>
                        <p class="text-muted">يدعم ملفات Excel (.xlsx, .xls) و CSV (.csv)</p>
                        <input type="file" id="importFile" accept=".xlsx,.xls,.csv" style="display: none;">
                    </div>
                    <div class="progress" id="importProgress" style="display: none;">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <div id="importResults" style="display: none;" class="mt-3">
                        <div class="alert alert-info">
                            <h6>نتائج الاستيراد:</h6>
                            <ul id="importResultsList"></ul>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" id="startImport" onclick="startImport()" disabled>بدء الاستيراد</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="ادارة_الطلاب.js"></script>
</body>
</html>

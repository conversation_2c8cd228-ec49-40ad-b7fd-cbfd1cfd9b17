// نظام إدارة الطلاب - JavaScript
// ملف الوظائف التفاعلية لصفحة إدارة الطلاب

// بيانات تجريبية للطلاب
let studentsData = [
    {
        id: 1,
        studentId: '1234567890',
        name: 'أحم<PERSON> محمد العلي',
        birthDate: '2010-05-15',
        birthPlace: 'الرياض',
        nationality: 'سعودي',
        class: 'أول متوسط أ',
        classId: 1,
        guardianName: 'محمد العلي',
        guardianPhone: '0501234567',
        guardianId: '1234567891',
        email: '<EMAIL>',
        address: 'الرياض - حي النخيل',
        status: 'منتظم',
        photo: 'https://via.placeholder.com/40x40/3498db/ffffff?text=أ',
        notes: '',
        registrationDate: '2024-08-25'
    },
    {
        id: 2,
        studentId: '1234567892',
        name: 'فاطمة عبدالله السعد',
        birthDate: '2009-12-20',
        birthPlace: 'جدة',
        nationality: 'سعودي',
        class: 'ثاني متوسط أ',
        classId: 3,
        guardianName: 'عبدالله السعد',
        guardianPhone: '0507654321',
        guardianId: '1234567893',
        email: '<EMAIL>',
        address: 'الرياض - حي الملز',
        status: 'منتظم',
        photo: 'https://via.placeholder.com/40x40/e74c3c/ffffff?text=ف',
        notes: 'طالبة متفوقة',
        registrationDate: '2023-08-25'
    },
    {
        id: 3,
        studentId: '1234567894',
        name: 'خالد سعد الغامدي',
        birthDate: '2008-03-10',
        birthPlace: 'الدمام',
        nationality: 'سعودي',
        class: 'ثالث متوسط ب',
        classId: 6,
        guardianName: 'سعد الغامدي',
        guardianPhone: '0551234567',
        guardianId: '1234567895',
        email: '<EMAIL>',
        address: 'الرياض - حي العليا',
        status: 'منتظم',
        photo: 'https://via.placeholder.com/40x40/27ae60/ffffff?text=خ',
        notes: '',
        registrationDate: '2022-08-25'
    },
    {
        id: 4,
        studentId: '1234567896',
        name: 'نورا أحمد القحطاني',
        birthDate: '2010-08-05',
        birthPlace: 'الرياض',
        nationality: 'سعودي',
        class: 'أول متوسط ب',
        classId: 2,
        guardianName: 'أحمد القحطاني',
        guardianPhone: '0561234567',
        guardianId: '1234567897',
        email: '<EMAIL>',
        address: 'الرياض - حي الورود',
        status: 'محول',
        photo: 'https://via.placeholder.com/40x40/f39c12/ffffff?text=ن',
        notes: 'محولة من مدرسة أخرى',
        registrationDate: '2024-09-15'
    },
    {
        id: 5,
        studentId: '1234567898',
        name: 'عبدالرحمن علي الشهري',
        birthDate: '2009-11-30',
        birthPlace: 'أبها',
        nationality: 'سعودي',
        class: 'ثاني متوسط ب',
        classId: 4,
        guardianName: 'علي الشهري',
        guardianPhone: '0571234567',
        guardianId: '1234567899',
        email: '<EMAIL>',
        address: 'الرياض - حي الصحافة',
        status: 'منتظم',
        photo: 'https://via.placeholder.com/40x40/9b59b6/ffffff?text=ع',
        notes: 'يحتاج متابعة إضافية',
        registrationDate: '2023-08-25'
    }
];

let filteredData = [...studentsData];
let currentEditingId = null;

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    loadStudentsTable();
    setupEventListeners();
});

// تهيئة الصفحة
function initializePage() {
    updateStatistics();
    setupDataTable();
}

// تحديث الإحصائيات
function updateStatistics() {
    const total = studentsData.length;
    const active = studentsData.filter(s => s.status === 'منتظم').length;
    const graduated = studentsData.filter(s => s.status === 'متخرج').length;
    const transferred = studentsData.filter(s => s.status === 'محول').length;
    
    document.getElementById('totalStudents').textContent = total;
    document.getElementById('activeStudents').textContent = active;
    document.getElementById('graduatedStudents').textContent = graduated;
    document.getElementById('transferredStudents').textContent = transferred;
}

// إعداد جدول البيانات
function setupDataTable() {
    if ($.fn.DataTable.isDataTable('#studentsTable')) {
        $('#studentsTable').DataTable().destroy();
    }
    
    $('#studentsTable').DataTable({
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        pageLength: 25,
        responsive: true,
        order: [[1, 'asc']],
        columnDefs: [
            { orderable: false, targets: [0, 9] }
        ]
    });
}

// تحميل جدول الطلاب
function loadStudentsTable() {
    const tbody = document.getElementById('studentsTableBody');
    tbody.innerHTML = '';
    
    filteredData.forEach(student => {
        const row = createStudentRow(student);
        tbody.appendChild(row);
    });
    
    setupDataTable();
}

// إنشاء صف طالب
function createStudentRow(student) {
    const row = document.createElement('tr');
    
    const statusClass = getStatusClass(student.status);
    const statusText = student.status;
    
    row.innerHTML = `
        <td>
            <img src="${student.photo}" alt="${student.name}" class="student-photo">
        </td>
        <td>${student.id}</td>
        <td><strong>${student.name}</strong></td>
        <td>${student.studentId}</td>
        <td>${student.class}</td>
        <td>${formatDate(student.birthDate)}</td>
        <td>${student.guardianName}</td>
        <td>${student.guardianPhone}</td>
        <td>
            <span class="status-badge ${statusClass}">${statusText}</span>
        </td>
        <td>
            <div class="action-buttons-table">
                <button class="btn btn-primary btn-sm" onclick="viewStudent(${student.id})" title="عرض">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-warning btn-sm" onclick="editStudent(${student.id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-danger btn-sm" onclick="deleteStudent(${student.id})" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
                <button class="btn btn-info btn-sm" onclick="printStudentCard(${student.id})" title="طباعة البطاقة">
                    <i class="fas fa-print"></i>
                </button>
            </div>
        </td>
    `;
    
    return row;
}

// الحصول على فئة الحالة
function getStatusClass(status) {
    switch(status) {
        case 'منتظم': return 'status-active';
        case 'محول': return 'status-transferred';
        case 'منقطع': return 'status-inactive';
        default: return 'status-active';
    }
}

// تنسيق التاريخ
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // البحث المباشر
    document.getElementById('searchInput').addEventListener('input', function() {
        applyFilters();
    });
    
    // مرشحات التصفية
    document.getElementById('classFilter').addEventListener('change', applyFilters);
    document.getElementById('statusFilter').addEventListener('change', applyFilters);
    document.getElementById('yearFilter').addEventListener('change', applyFilters);
    
    // منطقة الاستيراد
    const importArea = document.getElementById('importArea');
    const importFile = document.getElementById('importFile');
    
    importArea.addEventListener('click', () => importFile.click());
    importArea.addEventListener('dragover', handleDragOver);
    importArea.addEventListener('drop', handleDrop);
    importFile.addEventListener('change', handleFileSelect);
}

// تطبيق المرشحات
function applyFilters() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const classFilter = document.getElementById('classFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    const yearFilter = document.getElementById('yearFilter').value;
    
    filteredData = studentsData.filter(student => {
        const matchesSearch = student.name.toLowerCase().includes(searchTerm) || 
                            student.studentId.includes(searchTerm);
        const matchesClass = !classFilter || student.classId.toString() === classFilter;
        const matchesStatus = !statusFilter || student.status === statusFilter;
        const matchesYear = !yearFilter || student.registrationDate.includes(yearFilter.split('-')[0]);
        
        return matchesSearch && matchesClass && matchesStatus && matchesYear;
    });
    
    loadStudentsTable();
}

// مسح المرشحات
function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('classFilter').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('yearFilter').value = '';
    
    filteredData = [...studentsData];
    loadStudentsTable();
}

// إضافة طالب جديد
function addStudent() {
    currentEditingId = null;
    document.getElementById('studentModalTitle').textContent = 'إضافة طالب جديد';
    clearStudentForm();
    const modal = new bootstrap.Modal(document.getElementById('studentModal'));
    modal.show();
}

// تعديل طالب
function editStudent(id) {
    currentEditingId = id;
    const student = studentsData.find(s => s.id === id);
    if (!student) return;
    
    document.getElementById('studentModalTitle').textContent = 'تعديل بيانات الطالب';
    fillStudentForm(student);
    const modal = new bootstrap.Modal(document.getElementById('studentModal'));
    modal.show();
}

// عرض تفاصيل الطالب
function viewStudent(id) {
    const student = studentsData.find(s => s.id === id);
    if (!student) return;
    
    alert(`تفاصيل الطالب:\n\nالاسم: ${student.name}\nرقم الهوية: ${student.studentId}\nالصف: ${student.class}\nولي الأمر: ${student.guardianName}\nالهاتف: ${student.guardianPhone}\nالحالة: ${student.status}`);
}

// حذف طالب
function deleteStudent(id) {
    const student = studentsData.find(s => s.id === id);
    if (!student) return;
    
    if (confirm(`هل أنت متأكد من حذف الطالب "${student.name}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        studentsData = studentsData.filter(s => s.id !== id);
        filteredData = filteredData.filter(s => s.id !== id);
        loadStudentsTable();
        updateStatistics();
        showAlert('تم حذف الطالب بنجاح', 'success');
    }
}

// طباعة بطاقة الطالب
function printStudentCard(id) {
    const student = studentsData.find(s => s.id === id);
    if (!student) return;
    
    // إنشاء نافذة طباعة
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
        <head>
            <title>بطاقة الطالب - ${student.name}</title>
            <style>
                body { font-family: Arial, sans-serif; direction: rtl; text-align: right; }
                .card { border: 2px solid #333; padding: 20px; margin: 20px; }
                .header { text-align: center; border-bottom: 1px solid #333; padding-bottom: 10px; }
                .info { margin: 10px 0; }
                .photo { width: 100px; height: 100px; border: 1px solid #333; }
            </style>
        </head>
        <body>
            <div class="card">
                <div class="header">
                    <h2>مدرسة الأمل المتوسطة</h2>
                    <h3>بطاقة الطالب</h3>
                </div>
                <div class="info">
                    <p><strong>الاسم:</strong> ${student.name}</p>
                    <p><strong>رقم الهوية:</strong> ${student.studentId}</p>
                    <p><strong>الصف:</strong> ${student.class}</p>
                    <p><strong>تاريخ الميلاد:</strong> ${formatDate(student.birthDate)}</p>
                    <p><strong>ولي الأمر:</strong> ${student.guardianName}</p>
                    <p><strong>رقم الجوال:</strong> ${student.guardianPhone}</p>
                </div>
            </div>
        </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

// ملء نموذج الطالب
function fillStudentForm(student) {
    document.getElementById('studentId').value = student.studentId;
    document.getElementById('studentName').value = student.name;
    document.getElementById('birthDate').value = student.birthDate;
    document.getElementById('birthPlace').value = student.birthPlace;
    document.getElementById('nationality').value = student.nationality;
    document.getElementById('studentClass').value = student.classId;
    document.getElementById('guardianName').value = student.guardianName;
    document.getElementById('guardianPhone').value = student.guardianPhone;
    document.getElementById('guardianId').value = student.guardianId;
    document.getElementById('email').value = student.email;
    document.getElementById('address').value = student.address;
    document.getElementById('studentStatus').value = student.status;
    document.getElementById('notes').value = student.notes;
}

// مسح نموذج الطالب
function clearStudentForm() {
    document.getElementById('studentForm').reset();
}

// حفظ الطالب
function saveStudent() {
    const form = document.getElementById('studentForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    const studentData = {
        studentId: document.getElementById('studentId').value,
        name: document.getElementById('studentName').value,
        birthDate: document.getElementById('birthDate').value,
        birthPlace: document.getElementById('birthPlace').value,
        nationality: document.getElementById('nationality').value,
        classId: parseInt(document.getElementById('studentClass').value),
        class: document.getElementById('studentClass').selectedOptions[0].text,
        guardianName: document.getElementById('guardianName').value,
        guardianPhone: document.getElementById('guardianPhone').value,
        guardianId: document.getElementById('guardianId').value,
        email: document.getElementById('email').value,
        address: document.getElementById('address').value,
        status: document.getElementById('studentStatus').value,
        notes: document.getElementById('notes').value
    };
    
    if (currentEditingId) {
        // تعديل طالب موجود
        const index = studentsData.findIndex(s => s.id === currentEditingId);
        if (index !== -1) {
            studentsData[index] = { ...studentsData[index], ...studentData };
            showAlert('تم تحديث بيانات الطالب بنجاح', 'success');
        }
    } else {
        // إضافة طالب جديد
        const newStudent = {
            id: Math.max(...studentsData.map(s => s.id)) + 1,
            ...studentData,
            photo: `https://via.placeholder.com/40x40/3498db/ffffff?text=${studentData.name.charAt(0)}`,
            registrationDate: new Date().toISOString().split('T')[0]
        };
        studentsData.push(newStudent);
        showAlert('تم إضافة الطالب بنجاح', 'success');
    }
    
    applyFilters();
    updateStatistics();
    bootstrap.Modal.getInstance(document.getElementById('studentModal')).hide();
}

// استيراد الطلاب
function importStudents() {
    const modal = new bootstrap.Modal(document.getElementById('importModal'));
    modal.show();
}

// معالجة السحب والإفلات
function handleDragOver(e) {
    e.preventDefault();
    e.currentTarget.classList.add('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    e.currentTarget.classList.remove('dragover');
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFileSelect({ target: { files } });
    }
}

// معالجة اختيار الملف
function handleFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
        document.getElementById('startImport').disabled = false;
        showAlert(`تم اختيار الملف: ${file.name}`, 'info');
    }
}

// بدء الاستيراد
function startImport() {
    const progressBar = document.querySelector('#importProgress .progress-bar');
    const progress = document.getElementById('importProgress');
    const results = document.getElementById('importResults');
    const resultsList = document.getElementById('importResultsList');
    
    progress.style.display = 'block';
    
    // محاكاة عملية الاستيراد
    let progressValue = 0;
    const interval = setInterval(() => {
        progressValue += 10;
        progressBar.style.width = progressValue + '%';
        
        if (progressValue >= 100) {
            clearInterval(interval);
            
            // إظهار النتائج
            results.style.display = 'block';
            resultsList.innerHTML = `
                <li>تم استيراد 25 طالب بنجاح</li>
                <li>تم تحديث 5 طلاب موجودين</li>
                <li>تم تجاهل 2 سجل مكرر</li>
                <li>فشل في استيراد 1 سجل (بيانات ناقصة)</li>
            `;
            
            showAlert('تم الاستيراد بنجاح', 'success');
        }
    }, 200);
}

// تصدير البيانات
function exportStudents() {
    const csvContent = generateCSV();
    downloadCSV(csvContent, 'students_data.csv');
    showAlert('تم تصدير البيانات بنجاح', 'success');
}

// إنتاج ملف CSV
function generateCSV() {
    const headers = ['رقم الطالب', 'الاسم', 'رقم الهوية', 'الصف', 'ولي الأمر', 'رقم الجوال', 'الحالة'];
    const rows = filteredData.map(student => [
        student.id,
        student.name,
        student.studentId,
        student.class,
        student.guardianName,
        student.guardianPhone,
        student.status
    ]);
    
    return [headers, ...rows].map(row => row.join(',')).join('\n');
}

// تحميل ملف CSV
function downloadCSV(content, filename) {
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    link.click();
}

// طباعة القائمة
function printList() {
    window.print();
}

// العودة للصفحة الرئيسية
function goBack() {
    window.location.href = 'الواجهة_الرئيسية.html';
}

// إظهار التنبيهات
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}

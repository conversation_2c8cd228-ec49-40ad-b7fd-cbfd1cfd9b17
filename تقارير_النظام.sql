-- تقارير نظام إدارة المدرسة المتوسطة
-- هذا الملف يحتوي على استعلامات SQL لإنتاج التقارير المختلفة

-- ====================
-- تقرير كشف الحضور اليومي
-- ====================

-- كشف حضور الطلاب لصف معين في تاريخ محدد
SELECT 
    p.رقم_الجلوس,
    p.الاسم_الكامل AS [اسم الطالب],
    c.اسم_الصف AS [الصف],
    a.التاريخ,
    a.حالة_الحضور AS [الحضور],
    a.وقت_الوصول AS [وقت الوصول],
    a.سبب_الغياب AS [سبب الغياب],
    t.الاسم_الكامل AS [المعلم المسجل]
FROM ((الطلاب p 
    INNER JOIN الصفوف c ON p.رقم_الصف = c.رقم_الصف)
    LEFT JOIN حضور_الطلاب a ON p.رقم_الطالب = a.رقم_الطالب)
    LEFT JOIN المعلمين t ON a.رقم_المعلم_المسجل = t.رقم_المعلم
WHERE p.حالة_النشاط = True 
    AND c.رقم_الصف = [أدخل رقم الصف]
    AND a.التاريخ = [أدخل التاريخ]
ORDER BY p.رقم_الجلوس;

-- ====================
-- تقرير كشف الدرجات
-- ====================

-- كشف درجات الطلاب لمادة معينة في صف محدد
SELECT 
    p.رقم_الجلوس,
    p.الاسم_الكامل AS [اسم الطالب],
    c.اسم_الصف AS [الصف],
    m.اسم_المادة AS [المادة],
    g.نوع_التقييم AS [نوع التقييم],
    g.الدرجة AS [الدرجة المحصلة],
    g.الدرجة_النهائية AS [الدرجة النهائية],
    Round((g.الدرجة / g.الدرجة_النهائية) * 100, 2) AS [النسبة المئوية],
    IIf(g.الدرجة >= (g.الدرجة_النهائية * 0.5), "ناجح", "راسب") AS [النتيجة],
    g.التاريخ AS [تاريخ التقييم],
    t.الاسم_الكامل AS [المعلم]
FROM (((الطلاب p 
    INNER JOIN الصفوف c ON p.رقم_الصف = c.رقم_الصف)
    INNER JOIN الدرجات g ON p.رقم_الطالب = g.رقم_الطالب)
    INNER JOIN المواد_الدراسية m ON g.رقم_المادة = m.رقم_المادة)
    LEFT JOIN المعلمين t ON g.رقم_المعلم_المدخل = t.رقم_المعلم
WHERE p.حالة_النشاط = True 
    AND c.رقم_الصف = [أدخل رقم الصف]
    AND m.رقم_المادة = [أدخل رقم المادة]
    AND g.العام_الدراسي = [أدخل العام الدراسي]
    AND g.الفصل_الدراسي = [أدخل الفصل الدراسي]
ORDER BY p.رقم_الجلوس, g.التاريخ;

-- ====================
-- تقرير شهادة الطالب
-- ====================

-- شهادة درجات طالب لجميع المواد
SELECT 
    p.الاسم_الكامل AS [اسم الطالب],
    p.رقم_الهوية AS [رقم الهوية],
    c.اسم_الصف AS [الصف],
    p.العام_الدراسي AS [العام الدراسي],
    p.الفصل_الدراسي AS [الفصل الدراسي],
    m.اسم_المادة AS [المادة],
    Sum(IIf(g.نوع_التقييم = "أعمال السنة", g.الدرجة, 0)) AS [أعمال السنة],
    Sum(IIf(g.نوع_التقييم = "اختبار نهائي", g.الدرجة, 0)) AS [الاختبار النهائي],
    Sum(g.الدرجة) AS [المجموع],
    m.الدرجة_النهائية AS [الدرجة النهائية],
    Round((Sum(g.الدرجة) / m.الدرجة_النهائية) * 100, 2) AS [النسبة المئوية],
    IIf(Sum(g.الدرجة) >= (m.الدرجة_النهائية * 0.5), "ناجح", "راسب") AS [النتيجة]
FROM (((الطلاب p 
    INNER JOIN الصفوف c ON p.رقم_الصف = c.رقم_الصف)
    INNER JOIN الدرجات g ON p.رقم_الطالب = g.رقم_الطالب)
    INNER JOIN المواد_الدراسية m ON g.رقم_المادة = m.رقم_المادة)
WHERE p.رقم_الطالب = [أدخل رقم الطالب]
    AND g.العام_الدراسي = [أدخل العام الدراسي]
    AND g.الفصل_الدراسي = [أدخل الفصل الدراسي]
GROUP BY p.الاسم_الكامل, p.رقم_الهوية, c.اسم_الصف, p.العام_الدراسي, p.الفصل_الدراسي, m.اسم_المادة, m.الدرجة_النهائية
ORDER BY m.اسم_المادة;

-- ====================
-- تقرير المخالفات السلوكية
-- ====================

-- تقرير المخالفات السلوكية لطالب معين
SELECT 
    p.الاسم_الكامل AS [اسم الطالب],
    p.رقم_الهوية AS [رقم الهوية],
    c.اسم_الصف AS [الصف],
    v.نوع_المخالفة AS [نوع المخالفة],
    v.وصف_المخالفة AS [وصف المخالفة],
    v.درجة_المخالفة AS [درجة المخالفة],
    v.التاريخ AS [تاريخ المخالفة],
    t.الاسم_الكامل AS [المعلم المبلغ],
    v.الاجراء_المتخذ AS [الإجراء المتخذ],
    v.حالة_المخالفة AS [حالة المخالفة]
FROM ((الطلاب p 
    INNER JOIN الصفوف c ON p.رقم_الصف = c.رقم_الصف)
    INNER JOIN المخالفات_السلوكية v ON p.رقم_الطالب = v.رقم_الطالب)
    LEFT JOIN المعلمين t ON v.رقم_المعلم_المبلغ = t.رقم_المعلم
WHERE p.رقم_الطالب = [أدخل رقم الطالب]
    OR [أدخل رقم الطالب] IS NULL
ORDER BY v.التاريخ DESC;

-- ====================
-- تقرير إحصائيات الحضور
-- ====================

-- إحصائيات الحضور والغياب لكل صف
SELECT 
    c.اسم_الصف AS [الصف],
    Count(p.رقم_الطالب) AS [عدد الطلاب],
    Count(a.رقم_الحضور) AS [إجمالي التسجيلات],
    Sum(IIf(a.حالة_الحضور = "حاضر", 1, 0)) AS [عدد الحضور],
    Sum(IIf(a.حالة_الحضور = "غائب", 1, 0)) AS [عدد الغياب],
    Sum(IIf(a.حالة_الحضور = "متأخر", 1, 0)) AS [عدد التأخير],
    Sum(IIf(a.حالة_الحضور = "مستأذن", 1, 0)) AS [عدد الاستئذان],
    Round((Sum(IIf(a.حالة_الحضور = "حاضر", 1, 0)) / Count(a.رقم_الحضور)) * 100, 2) AS [نسبة الحضور],
    Round((Sum(IIf(a.حالة_الحضور = "غائب", 1, 0)) / Count(a.رقم_الحضور)) * 100, 2) AS [نسبة الغياب]
FROM (الطلاب p 
    INNER JOIN الصفوف c ON p.رقم_الصف = c.رقم_الصف)
    LEFT JOIN حضور_الطلاب a ON p.رقم_الطالب = a.رقم_الطالب
WHERE p.حالة_النشاط = True
    AND a.التاريخ BETWEEN [تاريخ البداية] AND [تاريخ النهاية]
GROUP BY c.اسم_الصف, c.رقم_الصف
ORDER BY c.رقم_الصف;

-- ====================
-- تقرير أداء الطلاب
-- ====================

-- تقرير أداء الطلاب في جميع المواد
SELECT 
    p.الاسم_الكامل AS [اسم الطالب],
    c.اسم_الصف AS [الصف],
    Count(DISTINCT m.رقم_المادة) AS [عدد المواد],
    Sum(g.الدرجة) AS [مجموع الدرجات],
    Sum(m.الدرجة_النهائية) AS [مجموع الدرجات النهائية],
    Round((Sum(g.الدرجة) / Sum(m.الدرجة_النهائية)) * 100, 2) AS [النسبة المئوية العامة],
    Round(Avg((g.الدرجة / m.الدرجة_النهائية) * 100), 2) AS [متوسط النسب],
    Sum(IIf((g.الدرجة / m.الدرجة_النهائية) >= 0.5, 1, 0)) AS [عدد المواد الناجح فيها],
    Sum(IIf((g.الدرجة / m.الدرجة_النهائية) < 0.5, 1, 0)) AS [عدد المواد الراسب فيها],
    IIf(Sum(IIf((g.الدرجة / m.الدرجة_النهائية) < 0.5, 1, 0)) = 0, "ناجح", "راسب") AS [النتيجة العامة]
FROM (((الطلاب p 
    INNER JOIN الصفوف c ON p.رقم_الصف = c.رقم_الصف)
    INNER JOIN الدرجات g ON p.رقم_الطالب = g.رقم_الطالب)
    INNER JOIN المواد_الدراسية m ON g.رقم_المادة = m.رقم_المادة)
WHERE p.حالة_النشاط = True
    AND g.نوع_التقييم = "اختبار نهائي"
    AND g.العام_الدراسي = [أدخل العام الدراسي]
    AND g.الفصل_الدراسي = [أدخل الفصل الدراسي]
GROUP BY p.رقم_الطالب, p.الاسم_الكامل, c.اسم_الصف
ORDER BY [النسبة المئوية العامة] DESC;

-- ====================
-- تقرير أداء المعلمين
-- ====================

-- تقرير أداء المعلمين (عدد الطلاب، متوسط الدرجات)
SELECT 
    t.الاسم_الكامل AS [اسم المعلم],
    t.الرقم_الوظيفي AS [الرقم الوظيفي],
    m.اسم_المادة AS [المادة],
    Count(DISTINCT tm.رقم_الصف) AS [عدد الصفوف],
    Count(DISTINCT p.رقم_الطالب) AS [عدد الطلاب],
    Count(g.رقم_الدرجة) AS [عدد التقييمات],
    Round(Avg(g.الدرجة), 2) AS [متوسط الدرجات],
    Round(Avg((g.الدرجة / g.الدرجة_النهائية) * 100), 2) AS [متوسط النسب المئوية],
    Sum(IIf((g.الدرجة / g.الدرجة_النهائية) >= 0.5, 1, 0)) AS [عدد الناجحين],
    Sum(IIf((g.الدرجة / g.الدرجة_النهائية) < 0.5, 1, 0)) AS [عدد الراسبين],
    Round((Sum(IIf((g.الدرجة / g.الدرجة_النهائية) >= 0.5, 1, 0)) / Count(g.رقم_الدرجة)) * 100, 2) AS [نسبة النجاح]
FROM ((((المعلمين t 
    INNER JOIN تدريس_المواد tm ON t.رقم_المعلم = tm.رقم_المعلم)
    INNER JOIN المواد_الدراسية m ON tm.رقم_المادة = m.رقم_المادة)
    INNER JOIN الطلاب p ON tm.رقم_الصف = p.رقم_الصف)
    LEFT JOIN الدرجات g ON p.رقم_الطالب = g.رقم_الطالب AND m.رقم_المادة = g.رقم_المادة)
WHERE t.حالة_النشاط = True
    AND p.حالة_النشاط = True
    AND g.نوع_التقييم = "اختبار نهائي"
    AND g.العام_الدراسي = [أدخل العام الدراسي]
    AND g.الفصل_الدراسي = [أدخل الفصل الدراسي]
GROUP BY t.رقم_المعلم, t.الاسم_الكامل, t.الرقم_الوظيفي, m.اسم_المادة
ORDER BY t.الاسم_الكامل, m.اسم_المادة;

-- ====================
-- تقرير كشف اللجان والاختبارات
-- ====================

-- كشف توزيع الطلاب على لجان الاختبارات
SELECT 
    e.اسم_اللجنة AS [اسم اللجنة],
    e.رقم_القاعة AS [رقم القاعة],
    ed.رقم_الجلوس AS [رقم الجلوس],
    p.الاسم_الكامل AS [اسم الطالب],
    p.رقم_الهوية AS [رقم الهوية],
    c.اسم_الصف AS [الصف],
    m.اسم_المادة AS [المادة],
    e.تاريخ_الاختبار AS [تاريخ الاختبار],
    e.وقت_البداية AS [وقت البداية],
    e.وقت_النهاية AS [وقت النهاية],
    t1.الاسم_الكامل AS [المراقب الأول],
    t2.الاسم_الكامل AS [المراقب الثاني]
FROM (((((لجان_الاختبارات e 
    INNER JOIN توزيع_اللجان ed ON e.رقم_اللجنة = ed.رقم_اللجنة)
    INNER JOIN الطلاب p ON ed.رقم_الطالب = p.رقم_الطالب)
    INNER JOIN الصفوف c ON p.رقم_الصف = c.رقم_الصف)
    LEFT JOIN المواد_الدراسية m ON ed.رقم_المادة = m.رقم_المادة)
    LEFT JOIN المعلمين t1 ON e.رقم_المراقب_الاول = t1.رقم_المعلم)
    LEFT JOIN المعلمين t2 ON e.رقم_المراقب_الثاني = t2.رقم_المعلم
WHERE e.نوع_الاختبار = [أدخل نوع الاختبار]
    AND e.تاريخ_الاختبار = [أدخل تاريخ الاختبار]
    AND e.حالة_النشاط = True
ORDER BY e.اسم_اللجنة, ed.رقم_الجلوس;

-- ====================
-- تقرير الإحصائيات العامة
-- ====================

-- إحصائيات عامة للمدرسة
SELECT 
    'إجمالي الطلاب' AS [البيان],
    Count(*) AS [العدد],
    '' AS [ملاحظات]
FROM الطلاب 
WHERE حالة_النشاط = True

UNION ALL

SELECT 
    'إجمالي المعلمين' AS [البيان],
    Count(*) AS [العدد],
    '' AS [ملاحظات]
FROM المعلمين 
WHERE حالة_النشاط = True

UNION ALL

SELECT 
    'إجمالي الصفوف' AS [البيان],
    Count(*) AS [العدد],
    '' AS [ملاحظات]
FROM الصفوف 
WHERE حالة_النشاط = True

UNION ALL

SELECT 
    'إجمالي المواد الدراسية' AS [البيان],
    Count(*) AS [العدد],
    '' AS [ملاحظات]
FROM المواد_الدراسية 
WHERE حالة_النشاط = True

UNION ALL

SELECT 
    'إجمالي المخالفات السلوكية' AS [البيان],
    Count(*) AS [العدد],
    'هذا الشهر' AS [ملاحظات]
FROM المخالفات_السلوكية 
WHERE Month(التاريخ) = Month(Date()) AND Year(التاريخ) = Year(Date())

UNION ALL

SELECT 
    'متوسط نسبة الحضور' AS [البيان],
    Round(Avg(IIf(حالة_الحضور = "حاضر", 100, 0)), 2) AS [العدد],
    'هذا الشهر' AS [ملاحظات]
FROM حضور_الطلاب 
WHERE Month(التاريخ) = Month(Date()) AND Year(التاريخ) = Year(Date());

-- ====================
-- تقرير قائمة الطلاب المتفوقين
-- ====================

-- قائمة الطلاب المتفوقين (النسبة أكبر من 90%)
SELECT 
    p.الاسم_الكامل AS [اسم الطالب],
    c.اسم_الصف AS [الصف],
    Round((Sum(g.الدرجة) / Sum(m.الدرجة_النهائية)) * 100, 2) AS [النسبة المئوية],
    Count(DISTINCT m.رقم_المادة) AS [عدد المواد],
    p.اسم_ولي_الامر AS [ولي الأمر],
    p.رقم_جوال_ولي_الامر AS [رقم الجوال]
FROM (((الطلاب p 
    INNER JOIN الصفوف c ON p.رقم_الصف = c.رقم_الصف)
    INNER JOIN الدرجات g ON p.رقم_الطالب = g.رقم_الطالب)
    INNER JOIN المواد_الدراسية m ON g.رقم_المادة = m.رقم_المادة)
WHERE p.حالة_النشاط = True
    AND g.نوع_التقييم = "اختبار نهائي"
    AND g.العام_الدراسي = [أدخل العام الدراسي]
    AND g.الفصل_الدراسي = [أدخل الفصل الدراسي]
GROUP BY p.رقم_الطالب, p.الاسم_الكامل, c.اسم_الصف, p.اسم_ولي_الامر, p.رقم_جوال_ولي_الامر
HAVING Round((Sum(g.الدرجة) / Sum(m.الدرجة_النهائية)) * 100, 2) >= 90
ORDER BY [النسبة المئوية] DESC;
